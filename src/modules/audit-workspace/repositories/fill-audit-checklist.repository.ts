import { extend, filter, intersection, map } from 'lodash';
import * as momentTZ from 'moment-timezone';
import { BaseError, TokenPayloadModel, TypeORMRepository } from 'svm-nest-lib-v3';
import { Connection, EntityRepository } from 'typeorm';
import {
  AuditWorkspaceStatus,
  EmailTypeEnum,
  FillAuditChecklistStatus,
  MailTemplate,
  ModulePathEnum,
  PushTypeEnum,
  RemarkType,
} from '../../../commons/enums';
import APP_CONFIG from '../../../configs/app.config';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  IUser,
  NotificationProducer,
} from '../../../micro-services/async/notification.producer';
import { MasterTableObj } from '../../../modules/commons/master-table/data/master-table.data';
import { leadingZero } from '../../../utils';
import { ChkQuestion } from '../../audit-checklist/entity/chk-question.entity';
import { ChkQuestionAnswerRepository } from '../../audit-checklist/repository/chk-question-answer.repository';
import { ChkQuestionRepository } from '../../audit-checklist/repository/chk-question.repository';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { CompanyFeatureVersionRepository } from '../../commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../commons/company-feature-version/feature-version.config';
import { PlanningRequest } from '../../planning-request/entities/planning-request.entity';
import { PlanningRequestRepository } from '../../planning-request/repositories/planning-request.repository';
import { ModuleType } from '../../user-assignment/user-assignment.enum';
import { UserRepository } from '../../user/user.repository';
import { CreateAuditWorkspaceDto } from '../dto';
import { AuditWorkspace } from '../entities/audit-workspace.entity';
import { FillAuditChecklistQuestion } from '../entities/fill-audit-checklist-question.entity';
import { FillAuditChecklist } from '../entities/fill-audit-checklist.entity';
import { AuditWorkspaceRepository } from './audit-workspace.repository';
import { FillAuditChecklistQuestionRepository } from './fill-audit-checklist-question.repository';
import { checklistInstances } from '../enums';
import { InspectionMappingRepository } from 'src/modules/inspection-mapping/repositories/inspection-mapping.repository';

@EntityRepository(FillAuditChecklist)
export class FillAuditChecklistRepository extends TypeORMRepository<FillAuditChecklist> {
  constructor(
    private readonly connection: Connection,
    private readonly notificationProducer: NotificationProducer,
  ) {
    super();
  }
  async listFillChecklistByWorkspace(workSpaceId: string, user: TokenPayloadModel) {
    try {
      const auditWorkSpace = await this.connection
        .getCustomRepository(AuditWorkspaceRepository)
        .createQueryBuilder('auditWorkspace')
        .leftJoin('auditWorkspace.vessel', 'vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        .select(['auditWorkspace.id', 'vessel.id', 'vesselType.id', 'auditWorkspace.isSA'])
        .where(
          'auditWorkspace.id = :id AND auditWorkspace.companyId = :companyId AND auditWorkspace.deleted = false',
          {
            id: workSpaceId,
            companyId: user.companyId,
          },
        )
        .getOne();
      if (!auditWorkSpace) {
        throw new BaseError({ message: 'auditWorkspace.NOT_FOUND' });
      }
      if (auditWorkSpace?.isSA) {
        const data = await this.createQueryBuilder('fillChkList')
          .leftJoinAndSelect('fillChkList.selfAssessment', 'selfAssessment')
          .leftJoin('selfAssessment.standardMaster', 'standardMaster')
          .leftJoin('selfAssessment.createdUser', 'createdUser')
          .leftJoin('selfAssessment.updatedUser', 'updatedUser')
          .where('fillChkList.auditWorkspaceId = :id ', {
            id: workSpaceId,
          })
          .addSelect([
            'createdUser.username',
            'fillChkList.id',
            'updatedUser.username',
            'standardMaster.id',
            'standardMaster.name',
          ])
          .orderBy('fillChkList.createdAt', 'ASC')
          .getMany();
        return data;
      } else {
        const fillAuditChecklists = await this.createQueryBuilder('fillChkList')
          .leftJoinAndSelect('fillChkList.auditChecklist', 'auditChecklist')
          .leftJoinAndSelect('auditChecklist.questions', 'questions')
          .leftJoin('questions.referencesCategoryData', 'referencesCategoryData')
          .leftJoin(
            'questions.fillQuestions',
            'fillQuestions',
            'fillQuestions.fillAuditChecklistId = fillChkList.id',
          )
          .leftJoin('fillChkList.lastUpdatedBy', 'lastUpdatedBy')
          .leftJoin('fillChkList.auditType', 'auditType')
          .leftJoin('fillChkList.submitBy', 'submitBy')
          // .leftJoin('fillChkList.fillQuestions', 'fillQuestions')
          .leftJoin('fillQuestions.reportFindingItem', 'reportFindingItem')
          .addSelect([
            'lastUpdatedBy.username',
            'submitBy.username',
            'auditType.name',
            'auditType.code',
            'fillQuestions.id',
            'fillQuestions.fillAuditChecklistId',
            'fillQuestions.answers',
            'fillQuestions.reportFindingItemId',
            'fillQuestions.findingRemark',
            'fillQuestions.chkQuestionId',
            'fillQuestions.evidencePictures',
            'fillQuestions.attachments',
            'fillQuestions.reportFindingItemId',
            'reportFindingItem.findingComment',
            'reportFindingItem.id',
          ])
          .select()
          .where('fillChkList.auditWorkspaceId = :id ', {
            id: workSpaceId,
          })
          .andWhere(
            `(auditChecklist.auditEntity = 'Office' OR (auditChecklist.auditEntity = 'Vessel' AND referencesCategoryData.valueId = :vesselTypeId AND referencesCategoryData.masterTableId = :masterTableId))`,
            {
              vesselTypeId: auditWorkSpace?.vessel?.vesselType?.id || null,
              masterTableId: MasterTableObj.VESSEL_TYPE,
            },
          )
          .orderBy('fillQuestions.updatedAt', 'DESC')
          .getMany();

        // Reshape fill audit checklist
        for (let i = 0; i < fillAuditChecklists.length; i++) {
          fillAuditChecklists[i].fillQuestions = [];
          for (let j = 0; j < fillAuditChecklists[i].auditChecklist.questions.length; j++) {
            fillAuditChecklists[i].fillQuestions.push(
              ...fillAuditChecklists[i].auditChecklist.questions[j].fillQuestions,
            );
          }
        }

        const data = map(fillAuditChecklists, (o) =>
          extend(
            {
              listQuestions: o.fillQuestions.length,
              pendingQuestion:
                o.fillQuestions.length -
                this._countCompletedQuestions(o.fillQuestions, o.auditChecklist.questions),
              completedQuestion: this._countCompletedQuestions(
                o.fillQuestions,
                o.auditChecklist.questions,
              ),
              totalOfFinding: filter(o.fillQuestions, (o) => o.reportFindingItem?.findingComment)
                .length,
            },
            o,
          ),
        );
        return data;
      }
    } catch (ex) {
      throw ex;
    }
  }

  async listFillChecklistForExport(workSpaceId: string) {
    try {
      const auditChecklists = await this.createQueryBuilder('fillChkList')
        .leftJoinAndSelect('fillChkList.auditChecklist', 'auditChecklist')
        .leftJoinAndSelect('fillChkList.fillQuestions', 'fillQuestions')
        .leftJoinAndSelect('fillQuestions.chkQuestion', 'chkQuestion')
        .leftJoinAndSelect('chkQuestion.location', 'location')
        .leftJoin('chkQuestion.mainCategory', 'mainCategory')
        .leftJoinAndSelect('fillQuestions.reportFindingItem', 'reportFindingItem')
        .leftJoin('reportFindingItem.secondCategory', 'secondCategory')
        .leftJoin('reportFindingItem.thirdCategory', 'thirdCategory')
        .addSelect([
          'chkQuestion.id',
          'chkQuestion.code',
          'chkQuestion.question',
          'mainCategory.name',
          'mainCategory.acronym',
          'secondCategory.name',
          'secondCategory.acronym',
          'thirdCategory.name',
        ])
        .select()
        .where('fillChkList.auditWorkspaceId = :id ', {
          id: workSpaceId,
        })
        .getMany();

      for (let i = 0; i < auditChecklists.length; i++) {
        for (let j = 0; j < auditChecklists[i].fillQuestions.length; j++) {
          const chkQuestionId = auditChecklists[i].fillQuestions[j].chkQuestionId;
          const reportFindingItem = auditChecklists[i].fillQuestions[j].reportFindingItem;

          // get ref info
          const objRefCate = await this.manager
            .getCustomRepository(ChkQuestionRepository)
            .detailQuestionRefCategory(chkQuestionId);

          if (!reportFindingItem) {
            objRefCate.potential_risk = null;
            objRefCate.criticality = null;
          }

          // get answers
          const answerIds = auditChecklists[i].fillQuestions[j].answers;
          let answers;
          if (answerIds.length > 0) {
            answers = await this.manager
              .getCustomRepository(ChkQuestionAnswerRepository)
              ._getAnswers(answerIds);
          }
          console.log(answers);

          Object.assign(auditChecklists[i].fillQuestions[j], {
            answers,
          });

          auditChecklists[i].fillQuestions[j].reportFindingItem = {
            ...reportFindingItem,
            ...objRefCate,
          };
        }
      }
      return auditChecklists;
    } catch (ex) {
      throw ex;
    }
  }

  async listFillChecklistsByPR(planningRequestId: string, user: TokenPayloadModel) {
    try {
      // First, check if the planning request is for self assessment
      const planningRequest = await this.connection
        .getRepository(PlanningRequest)
        .createQueryBuilder('planningRequest')
        .select(['planningRequest.id', 'planningRequest.isSA'])
        .where('planningRequest.id = :planningRequestId', { planningRequestId })
        .getOne();

      if (planningRequest?.isSA) {
        // If it's a self assessment, fetch data from selfAssessment
        const fillChecklists = await this.createQueryBuilder('fillChkList')
          .leftJoin('fillChkList.auditWorkspace', 'auditWorkspace')
          .leftJoin('fillChkList.selfAssessment', 'selfAssessment')
          .leftJoin('selfAssessment.standardMaster', 'standardMaster')
          .leftJoin('fillChkList.auditType', 'auditType')
          .where(
            'auditWorkspace.planningRequestId = :planningRequestId AND auditWorkspace.companyId = :companyId',
            {
              planningRequestId,
              companyId: user.companyId,
            },
          )
          .select()
          .addSelect([
            'selfAssessment.id',
            'standardMaster.id',
            'standardMaster.name',
            'standardMaster.code',
            'auditType.name',
          ])
          .getMany();

        return fillChecklists;
      } else {
        // If it's not a self assessment, use the original implementation
        const fillChecklists = await this.createQueryBuilder('fillChkList')
          .leftJoin('fillChkList.auditWorkspace', 'auditWorkspace')
          .leftJoin('fillChkList.auditChecklist', 'auditChecklist')
          .leftJoin('fillChkList.auditType', 'auditType')
          .where(
            'auditWorkspace.planningRequestId = :planningRequestId AND auditWorkspace.companyId = :companyId',
            {
              planningRequestId,
              companyId: user.companyId,
            },
          )
          .select()
          .addSelect(['auditChecklist.code', 'auditChecklist.name', 'auditType.name'])
          .getMany();

        return fillChecklists;
      }
    } catch (ex) {
      throw ex;
    }
  }

  async triggerAwsAndFillChkList(
    auditWorkspaceId: string,
    fillChecklistId: string,
    user: TokenPayloadModel,
    body: CreateAuditWorkspaceDto,
  ) {
    const fillChecklist = await this.createQueryBuilder('fillChecklist')
      .leftJoin('fillChecklist.auditWorkspace', 'auditWorkspace')
      .where('fillChecklist.id = :id', { id: fillChecklistId })
      .addSelect([
        'auditWorkspace.id',
        'auditWorkspace.refNo',
        'auditWorkspace.isNew',
        'auditWorkspace.auditors',
        'auditWorkspace.status',
      ])
      .select()
      .getOne();

    const workspace = fillChecklist.auditWorkspace;
    const dataNoti: INotificationEventModel[] = [];
    const dataSendMail: IEmailEventModel[] = [];
    const transacion = await this.connection.transaction(async (manager) => {
      const currYear = momentTZ.tz(body.timezone).year();

      const pr = await this.manager
        .getRepository(PlanningRequest)
        .createQueryBuilder('planningRequest')
        .leftJoin('planningRequest.vessel', 'vessel')
        .leftJoin('planningRequest.company', 'company')
        .addSelect(['company.code', 'vessel.code'])
        .select()
        .where('planningRequest.id = :id', { id: body.planningRequestId })
        .getOne();

      if (workspace.isNew) {
        const awsRefNoCounter = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager: manager,
            companyId: user.companyId,
            feature: FeatureVersionConfig.AUDIT_INSPECTION_WORKSPACE_REF_NO,
            year: Number(currYear),
          });
        const refNo = this._genWorkspaceRefNo(awsRefNoCounter, Number(currYear), pr.company.code);
        await manager.update(
          AuditWorkspace,
          { id: auditWorkspaceId },
          {
            isNew: false,
            refNo: refNo,
            status: AuditWorkspaceStatus.DRAFT,
          },
        );

        // send notify when change status record workspace
        if (workspace.status === AuditWorkspaceStatus.NEW) {
          if (workspace.auditors && workspace.auditors.length > 0) {
            const listReceiverNoti = await manager
              .getCustomRepository(UserRepository)
              .listByIds(workspace.auditors);
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.AUDIT_WORKSPACE,
              recordId: auditWorkspaceId,
              recordRef: refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: 'In progress',
              previousStatus: AuditWorkspaceStatus.DRAFT,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: workspace.refNo,
                  recordId: auditWorkspaceId,
                  path: ModulePathEnum.AUDIT_INSPECTION_WORKSPACE,
                  currentStatus: 'In progress',
                  previousStatus: AuditWorkspaceStatus.DRAFT,
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
        }

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_WORKSPACE,
            planningId: pr.id,
          },
          user,
          [AuditActivityEnum.IN_PROGRESS],
          null,
        );
      }

      if (!fillChecklist.webInstance && !fillChecklist.appInstance) {
        const instanceCounter = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager: manager,
            companyId: user.companyId,
            feature: FeatureVersionConfig.FILL_AUDIT_CHECKLIST_INSTANCE,
            year: Number(currYear),
          });

        await manager.update(
          FillAuditChecklist,
          { id: fillChecklistId },
          {
            status: FillAuditChecklistStatus.IN_PROGRESS,
            webInstance: this._geneFillChecklistRefNo(instanceCounter, pr.company.code, currYear),
            appInstance: null,
            // submitOn: new Date(),
            // submitById: user.id,
          },
        );

        // trigger planing request status to In-Progress
        await manager
          .getCustomRepository(PlanningRequestRepository)
          ._triggerInProgressPlanningRequest(body.planningRequestId, user.id);

        //Trigger generate ROF
        // const rof = await manager.getCustomRepository(ReportFindingFormRepository).findOne({
        //   planningRequestId: workspace.planningRequestId,
        // });

        // if (!rof) {
        //   const createdUser = await manager
        //     .getCustomRepository(UserRepository)
        //     ._getUserInfoForHistory(user.id);

        //   const rofItems = await manager
        //     .getCustomRepository(ReportFindingItemRepository)
        //     .listFindingItemsByPR(workspace.planningRequestId, user);

        //   const rofForm: CreateReportFindingFormDto = {
        //     planningRequestId: workspace.planningRequestId,
        //     timezone: body.timezone,
        //     reportFindingItems: rofItems,
        //     previousNCFindings: [],
        //     officeComment: '',
        //     comments: [],
        //     workflowRemark: '',
        //     isSubmit: false,
        //   };

        //   await manager
        //     .getCustomRepository(ReportFindingFormRepository)
        //     ._createReportFindingFormHelper(manager, rofForm, user, createdUser);
        // }
      }
      return { dataNoti, dataSendMail };
    });

    // here getting InspectionCarriedIn and update to AuditWorkspace for AGGRID
    const InspectionCarriedIn = await this.getFillChecklistInstances(auditWorkspaceId);

    await this.manager.update(
      AuditWorkspace,
      { id: auditWorkspaceId },
      {
        inspectionCarriedIn: InspectionCarriedIn,
      },
    );
    return { dataNoti: transacion?.dataNoti, dataSendMail: transacion?.dataSendMail };
  }
  // function for get all checklist Instances.
  async getFillChecklistInstances(auditWorkspaceId: string) {
    const getFillChecklistInstances = await this.createQueryBuilder('fillChecklist')
      .where('fillChecklist.auditWorkspaceId = :auditWorkspaceId', {
        auditWorkspaceId: auditWorkspaceId,
      })
      .addSelect(['fillChecklist.webInstance', 'fillChecklist.appInstance'])
      .getMany();

    let webInstance = false;
    let appInstance = false;
    getFillChecklistInstances?.map((item) => {
      if (item?.webInstance) {
        webInstance = true;
      } else if (item?.appInstance) {
        appInstance = true;
      }
    });

    let InspectionCarriedIn = '';
    if (webInstance && appInstance) {
      InspectionCarriedIn = checklistInstances.WEB_APP;
    } else if (webInstance) {
      InspectionCarriedIn = checklistInstances.WEB;
    } else if (appInstance) {
      InspectionCarriedIn = checklistInstances.APP;
    }
    return InspectionCarriedIn;
  }

  async getAttByFindingItemIds(findingItemIds: string[], user?: TokenPayloadModel) {
    const queryBuilder = this.connection
      .getCustomRepository(FillAuditChecklistQuestionRepository)
      .createQueryBuilder('fillQuestions')
      .select([
        'fillQuestions.reportFindingItemId',
        'fillQuestions.attachments',
        'fillQuestions.evidencePictures',
      ])
      .where(' fillQuestions.deleted = FALSE ');
    if (findingItemIds.length) {
      queryBuilder.andWhere('fillQuestions.reportFindingItemId IN (:...findingItemIds)', {
        findingItemIds,
      });
    }
    const response = new Map<string, string[]>();
    const attachmentsFound = await queryBuilder.getMany();
    attachmentsFound.forEach((att) => {
      const listAtt = [];
      if (att.attachments.length) {
        listAtt.push(...att.attachments);
      }
      if (att.evidencePictures.length) {
        listAtt.push(...att.evidencePictures);
      }
      if (att.reportFindingItemId) {
        response.set(att.reportFindingItemId, listAtt);
      }
    });
    return response;
  }

  private _genWorkspaceRefNo(counter: number, currYear: number, companyCode: string) {
    return `${companyCode}/IW/${counter}/${currYear}`;
  }

  private _geneFillChecklistRefNo(counter: number, companyCode: string, currYear: number) {
    const version = leadingZero(counter, 5);
    return `CHF${companyCode + currYear + version}`;
  }

  private _countCompletedQuestions(
    fillQuestions: FillAuditChecklistQuestion[],
    checklistQuestions: ChkQuestion[],
  ) {
    let totalCompletedAnswers = 0;
    for (let i = 0; i < checklistQuestions.length; i++) {
      const checklistQuestion = checklistQuestions[i];
      const fillQuestion = fillQuestions.find((e) => e.chkQuestionId == checklistQuestion.id);
      let isCompletedAnswer = true;

      if (fillQuestion.answers.length == 0) {
        continue;
      }

      // check completed answer?
      if (
        checklistQuestion.hasRemark == RemarkType.ALL &&
        (fillQuestion.answers.length == 0 || !fillQuestion.reportFindingItemId)
      ) {
        isCompletedAnswer = false;
      } else if (checklistQuestion.hasRemark == RemarkType.SPECIFIC) {
        const specificAnswers = checklistQuestion.remarkSpecificAnswers;
        const chooseAnswers = fillQuestion.answers;
        const canCreateFindingItemAnswers = intersection(chooseAnswers, specificAnswers);

        // check if fill quest can create ROF-Item but not yet create.
        if (canCreateFindingItemAnswers.length != 0 && !fillQuestion.reportFindingItemId) {
          isCompletedAnswer = false;
        }
      }

      // check if evident pictures required
      if (checklistQuestion.requireEvidencePicture && fillQuestion.evidencePictures.length == 0) {
        isCompletedAnswer = false;
      }

      // check if min picture length satisfy
      if (checklistQuestion.minPictureRequired > fillQuestion.attachments.length) {
        isCompletedAnswer = false;
      }

      totalCompletedAnswers = isCompletedAnswer ? totalCompletedAnswers + 1 : totalCompletedAnswers;
    }

    return totalCompletedAnswers;
  }
}
