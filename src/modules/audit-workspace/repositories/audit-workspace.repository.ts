import { cloneDeep } from 'lodash';
import * as momentTZ from 'moment-timezone';
import { CatalogConst } from 'src/modules-qa/catalog/catalog-key.const';
import { MetaConfig } from 'src/modules-qa/catalog/entity/meta-config.entity';
import { InspectionMappingRepository } from 'src/modules/inspection-mapping/repositories/inspection-mapping.repository';
import {
  convertFilterField,
  createSelectSql,
  createWhereSql,
  handleGetDataForAGGrid,
  isDoingGrouping,
  PayloadAGGridDto,
} from 'src/utils';
import {
  BaseError,
  LoggerCommon,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityManager, EntityRepository, getConnection } from 'typeorm';
import { DBErrorCode } from '../../../commons/consts/db.const';
import { TimezoneDTO } from '../../../commons/dtos';
import { UploadDTO } from '../../../commons/dtos/upload.dto';
import {
  AuditEntity,
  AuditWorkspaceStatus,
  CompanyLevelEnum,
  EmailTypeEnum,
  FillAuditChecklistStatus,
  MailTemplate,
  ModulePathEnum,
  PushTypeEnum,
  SelfAssessmentMonthEnum,
} from '../../../commons/enums';
import { CreatedUserHistoryModel } from '../../../commons/models';
import APP_CONFIG from '../../../configs/app.config';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  IUser,
  NotificationProducer,
} from '../../../micro-services/async/notification.producer';
import { AuditChecklist } from '../../audit-checklist/entity/audit-checklist.entity';
import { AuditChecklistRepository } from '../../audit-checklist/repository/audit-checklist.repository';
import { ChkQuestionRepository } from '../../audit-checklist/repository/chk-question.repository';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { CompanyFeatureVersionRepository } from '../../commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../commons/company-feature-version/feature-version.config';
import { NatureFindingRepository } from '../../nature_finding/nature-finding.repository';
import { PlanningRequestRepository } from '../../planning-request/repositories/planning-request.repository';
import { CreateReportFindingFormDto } from '../../report-finding/dto';
import { ReportFindingFormRepository } from '../../report-finding/repositories/report-finding-form.repository';
import { ModuleType } from '../../user-assignment/user-assignment.enum';
import { UserRepository } from '../../user/user.repository';
import { CreateAuditWorkspaceDto, UpdateShowPopupAnalyticalReportDto } from '../dto';
import {
  AUDIT_WORKSPACE_FILTER_FIELDS,
  ListAuditWorkspaceDto,
} from '../dto/list-audit-workspace.dto';
import { UpdateMasterOrChiefOfEngineerDto } from '../dto/master-or-chief-of-engineer.dto';
import { AuditWorkspace } from '../entities/audit-workspace.entity';
import { FillAuditChecklistQuestion } from '../entities/fill-audit-checklist-question.entity';
import { FillAuditChecklist } from '../entities/fill-audit-checklist.entity';
import { ReportFindingItem } from '../entities/report-finding-item.entity';
import { checklistInstances } from '../enums';
import { ReportFindingItemRepository } from './report-finding-item.repository';
import { ObservedRiskRepository } from '../../risks/repositories/observed-risk.repository';
import {
  _supportCheckRoleScopeForGetList,
  _supportWhereDOCChartererOwner,
} from '../../../commons/functions';
import { SelfAssessmentRepository } from 'src/modules-qa/self-assessment/repository/self-assessment.repository';
import { SelfAssessment } from 'src/modules-qa/self-assessment/entity/self-assessment.entity';
import { FillSAChecklistQuestion } from '../entities/fill-sa-checklist-question.entity';
import { SAFindingItem } from '../entities/sa-finding-items.entity';

@EntityRepository(AuditWorkspace)
export class AuditWorkspaceRepository extends TypeORMRepository<AuditWorkspace> {
  constructor(
    private readonly connection: Connection,
    private readonly notificationProducer: NotificationProducer,
  ) {
    super();
  }

  async _triggerCreateAuditWorkspace(
    managerTrans: EntityManager,
    body: CreateAuditWorkspaceDto,
    user?: TokenPayloadModel,
    createdUser?: CreatedUserHistoryModel,
  ) {
    try {
      const currYear = momentTZ.tz(body.timezone).year();
      const currMonth = momentTZ.tz(body.timezone).month();

      const pr = await this.manager
        .getCustomRepository(PlanningRequestRepository)
        ._getVesselAndAuditorsByPR(
          user?.companyId || createdUser?.companyId,
          body.planningRequestId,
        );

      const auditorIds = pr.auditors.map((auditor) => auditor.id);

      const auditChecklists = await this.manager
        .getCustomRepository(AuditChecklistRepository)
        ._listValidAuditCheckListsByPR(
          body.planningRequestId,
          null,
          [],
          ['id', 'question.id', 'question.locationId', 'inspectionMapping.id', 'auditType.id'],
          pr.entityType,
        );

      const inspectionMappingByAuditType = await this.manager
        .getCustomRepository(InspectionMappingRepository)
        ._listValidInspectionMappingByAT(body.auditTypeIds, ['id', 'isSA']);

      const inspectionMappingByAuditTypeIds = inspectionMappingByAuditType.map(
        (inspectionMapping) => inspectionMapping.id,
      );

      const inspectionMappingIds = auditChecklists
        .reduce((arr: string[], item) => {
          arr.push(...item.inspectionMappings.map((im) => im.id));
          return arr;
        }, [])
        .concat(inspectionMappingByAuditTypeIds);

      const listUniqueInspectionMappingIds = Array.from(new Set(inspectionMappingIds));

      const natureFindings = await this.manager
        .getCustomRepository(NatureFindingRepository)
        ._listNatureFindingByPR(
          {
            companyId: user?.companyId || createdUser.companyId,
            workSpace: true,
            planningRequestId: body.planningRequestId,
            page: 1,
            pageSize: -1,
          },
          ['id'],
        );

      //  create workspace serial-code
      const awsSerialCodeCounter = await managerTrans
        .getCustomRepository(CompanyFeatureVersionRepository)
        .getNextVersion({
          manager: managerTrans,
          companyId: user?.companyId || createdUser.companyId,
          feature: FeatureVersionConfig.AUDIT_WORKSPACE_SERIAL_CODE,
          year: Number(currYear),
        });

      const workspace = await managerTrans.save(AuditWorkspace, {
        planningRequestId: pr.id,
        entityType: pr.entityType,
        vesselId: pr.vesselId,
        auditors: auditorIds,
        companyId: user?.companyId || createdUser.companyId,
        createdUserId: user?.id || createdUser.id,
        natureFindings: natureFindings.data.map((finding) => finding.id),
        inspectionMappingIds: listUniqueInspectionMappingIds,
        serialNo: this._genWorkspaceSerialCode(
          awsSerialCodeCounter,
          currYear,
          currMonth,
          pr.vessel?.code || pr.department?.code || 'EMPTY',
        ),
        isSA: pr?.isSA,
      });

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.INSPECTION_WORKSPACE,
          planningId: pr.id,
        },
        null,
        [AuditActivityEnum.CREATED],
        createdUser,
      );

      await this._initFillAuditChecklistAndQuestions(managerTrans, workspace.id, auditChecklists);
      return 1;
    } catch (ex) {
      LoggerCommon.error('[AuditWorkspaceRepository] createAuditWorkspace error', ex);
      if (ex.constraint === DBErrorCode.UNIQUE_VIOLATION) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.PLANNING_REQUEST_IS_DUPLICATED',
        });
      }
      throw ex;
    }
  }

  async _triggerCreateAuditWorkspaceForSA(
    managerTrans: EntityManager,
    body: CreateAuditWorkspaceDto,
    user?: TokenPayloadModel,
    createdUser?: CreatedUserHistoryModel,
  ) {
    try {
      const currYear = momentTZ.tz(body.timezone).year();
      const currMonth = momentTZ.tz(body.timezone).month();

      const pr = await this.manager
        .getCustomRepository(PlanningRequestRepository)
        ._getVesselAndAuditorsByPR(
          user?.companyId || createdUser?.companyId,
          body.planningRequestId,
        );

      const auditorIds = pr.auditors.map((auditor) => auditor.id);

      const saStandardChecklists = await this.manager
        .getCustomRepository(SelfAssessmentRepository)
        ._listValidSACheckListsByPR(body.planningRequestId, null, [], [], pr.entityType, pr?.auditCompanyId);

      const inspectionMappingByAuditType = await this.manager
        .getCustomRepository(InspectionMappingRepository)
        ._listValidInspectionMappingByAT(body.auditTypeIds, ['id']);

      const inspectionMappingByAuditTypeIds = inspectionMappingByAuditType.map(
        (inspectionMapping) => inspectionMapping.id,
      );

      const saStandardChecklistIds = saStandardChecklists
        .reduce((arr: string[], item) => {
          arr.push(...item?.standardMaster?.inspectionMapping?.map((im) => im?.id));
          return arr;
        }, [])
        .concat(inspectionMappingByAuditTypeIds);

      const listUniqueInspectionMappingIds = Array.from(new Set(saStandardChecklistIds));

      const natureFindings = await this.manager
        .getCustomRepository(NatureFindingRepository)
        ._listNatureFindingByPR(
          {
            companyId: user?.companyId || createdUser.companyId,
            workSpace: true,
            planningRequestId: body.planningRequestId,
            page: 1,
            pageSize: -1,
          },
          ['id'],
        );

      //  create workspace serial-code
      const awsSerialCodeCounter = await managerTrans
        .getCustomRepository(CompanyFeatureVersionRepository)
        .getNextVersion({
          manager: managerTrans,
          companyId: user?.companyId || createdUser.companyId,
          feature: FeatureVersionConfig.AUDIT_WORKSPACE_SERIAL_CODE,
          year: Number(currYear),
        });
      const workspace = await managerTrans.save(AuditWorkspace, {
        planningRequestId: pr?.id,
        entityType: pr?.entityType,
        vesselId: pr?.vesselId,
        auditors: auditorIds,
        companyId: user?.companyId || createdUser.companyId,
        createdUserId: user?.id || createdUser.id,
        natureFindings: natureFindings.data.map((finding) => finding.id),
        inspectionMappingIds: listUniqueInspectionMappingIds,
        serialNo: this._genWorkspaceSerialCode(
          awsSerialCodeCounter,
          currYear,
          currMonth,
          pr.vessel?.code || pr.department?.code || 'EMPTY',
        ),
        isSA: pr?.isSA,
      });

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.INSPECTION_WORKSPACE,
          planningId: pr.id,
        },
        null,
        [AuditActivityEnum.CREATED],
        createdUser,
      );
      await this._initFillSAChecklistAndQuestions(managerTrans, workspace.id, saStandardChecklists);
      return 1;
    } catch (ex) {
      LoggerCommon.error('[AuditWorkspaceRepository] createAuditWorkspace error', ex);
      if (ex.constraint === DBErrorCode.UNIQUE_VIOLATION) {
        throw new BaseError({
          status: 400,
          message: 'reportFindingForm.PLANNING_REQUEST_IS_DUPLICATED',
        });
      }
      throw ex;
    }
  }

  // Add submitted date month-year for audit workspace
  async _migrateSubmittedDateAuditWorspace() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_SUBMITTED_DATE_AUDIT_WORKSPACE,
      })
      .getOne();
    const version = '2023-12-05T14:20:00.000z';
    if (!metaConfig || version > metaConfig?.lastTimeSync) {
      return await this.connection.transaction(async (managerTrans) => {
        const listAWUpdate = await managerTrans
          .createQueryBuilder(AuditWorkspace, 'aw')
          .select()
          .where('deleted = FALSE')
          .andWhere(
            ' aw.submittedDate IS NOT NULL and (aw.submittedDate_Month IS NULL OR aw.submittedDate_Year IS NULL)',
          )
          .getMany();
        if (listAWUpdate.length) {
          for (let i = 0; i < listAWUpdate.length; i++) {
            listAWUpdate[i].submittedDate_Month = (
              listAWUpdate[i].submittedDate.getMonth() + 1
            ).toString();
            listAWUpdate[0].submittedDate_Year = listAWUpdate[0].submittedDate.getFullYear();

            await managerTrans.update(
              AuditWorkspace,
              { id: listAWUpdate[i].id },
              {
                submittedDate_Month: listAWUpdate[i].submittedDate.toLocaleString('en-US', {
                  month: 'short',
                }),
                submittedDate_Year: listAWUpdate[0].submittedDate.getFullYear(),
              },
            );
          }
        }
        await managerTrans.save(MetaConfig, {
          key: CatalogConst.MIGRATE_SUBMITTED_DATE_AUDIT_WORKSPACE,
          lastTimeSync: version,
        });
      });
    }
    return 1;
  }

  private _createQueryListAuditWorkspace() {
    return (
      this.createQueryBuilder('workSpace')
        .leftJoin('workSpace.vessel', 'vessel')
        .leftJoinAndSelect('workSpace.planningRequest', 'planningRequest')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin(
          'vessel.vesselDocHolders',
          'vesselDocHolders',
          `vesselDocHolders.vesselId = vessel.id and vesselDocHolders.status = 'active'`,
        )
        .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .leftJoin('workSpace.createdUser', 'createdUser')
        .leftJoin('workSpace.updatedUser', 'updatedUser')
        .leftJoin('planningRequest.auditCompany', 'auditCompany')
        .leftJoin('planningRequest.departments', 'departments')
        .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
        .leftJoin('planningRequest.voyageType', 'voyageType')
        .leftJoin('workSpace.company', 'company')
        .leftJoin('vessel.country', 'country')
        // .leftJoin('workSpace.fillAuditChecklists', 'fillAuditChecklists')
        .select()
        .addSelect([
          'createdUser.username',
          'updatedUser.username',
          'vessel.code',
          'vessel.id',
          'vessel.name',
          // 'vessel.countryFlag',
          'vessel.countryId',
          'vessel.country',
          'country.id',
          'country.name',
          'vesselType.name',
          // 'auditCompany.name',
          'departments.name',
          'company.id',
          'company.name',
          'vesselDocHolders.id',
          'vesselDocHolders.companyId',
          'vesselDocHolders.fromDate',
          'vesselDocHolders.toDate',
          'vesselDocHolders.responsiblePartyInspection',
          'vesselDocHolders.responsiblePartyQA',
          'vesselDocHolders.status',
          // 'fillAuditChecklists.appInstance',
          // 'fillAuditChecklists.webInstance',
          'auditTimeTable.actualFrom',
          'auditTimeTable.actualTo',
          'auditTimeTable.actualFrom_Year',
          'auditTimeTable.actualTo_Year',
          'auditTimeTable.actualFrom_Month',
          'auditTimeTable.actualTo_Month',
          'auditCompany.name',
          'companyVesselDocHolders.name',
          'voyageType.name',
        ])
    );
  }

  async listAuditWorkspace(
    query: ListAuditWorkspaceDto,
    user: TokenPayloadModel,
    body?: PayloadAGGridDto,
  ) {
    try {
      // let whereCondition =
      //   '(workSpace.companyId = :companyId AND :userId = ANY(workSpace.auditors))';
      // if (user.roleScope === RoleScope.ADMIN) {
      //   whereCondition = '(workSpace.companyId = :companyId)';
      // }
      const queryBuilder = this._createQueryListAuditWorkspace().where(
        `( workSpace.companyId = '${user.companyId}' )`,
      );

      const fieldSelects = [
        // 'auditCompany.name',
        'departments.name',
        'vessel.id',
        'vessel.name',
        'vessel.code',
        'vessel.countryId',
        'vessel.country',
        'country.id',
        'country.name',
        'vessel.vesselType',
        'vesselType.id',
        'vesselType.name',
        'company.id',
        'company.name',
        'createdUser.username',
        'updatedUser.username',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
        // 'fillAuditChecklists.appInstance',
        // 'fillAuditChecklists.webInstance',
        'auditTimeTable.actualFrom',
        'auditTimeTable.actualTo',
        'auditTimeTable.actualFrom_Year',
        'auditTimeTable.actualTo_Year',
        'auditTimeTable.actualFrom_Month',
        'auditTimeTable.actualTo_Month',
        'auditCompany.id',
        'voyageType.name',
      ];

      if (query?.content) {
        queryBuilder.andWhere(
          `("workSpace"."refNo" LIKE '%${query.content}%' OR "planningRequest"."refId" LIKE '%${query.content}%' OR vessel.name LIKE '%${query.content}%')`,
        );
      }
      if (query?.entityType) {
        queryBuilder.andWhere(` workSpace.entityType = '${query.entityType}' `);
      }

      if (query?.planningFrom) {
        queryBuilder.andWhere(` planningRequest.plannedFromDate >= '${query.planningFrom}' `);
      }

      if (query?.planningTo) {
        queryBuilder.andWhere(` planningRequest.plannedFromDate <= '${query.planningTo}'`);
      }

      if (query?.status) {
        queryBuilder.andWhere(`workSpace.status = '${query.status}'`);
      }

      //For homepage
      if (query.ids?.length) {
        queryBuilder.andWhere('workSpace.id IN (:...ids)', {
          ids: query.ids,
        });

        return await this.list(
          { page: query.page, limit: query.pageSize },
          {
            queryBuilder,
            sort: query.sort || 'workSpace.createdAt:-1',
            advanceConditions: {
              createdAtFrom: query.createdAtFrom,
              createdAtTo: query.createdAtTo,
            },
          },
        );
      }

      if (!RoleScopeCheck.isAdmin(user)) {
        const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
          this.manager,
          user.explicitCompanyId,
          'workSpace',
        );
        queryBuilder
          .leftJoin('planningRequest.auditors', 'auditors')
          .leftJoin('planningRequest.userAssignments', 'userAssignments')
          .leftJoin('userAssignments.user', 'user')
          .leftJoin('vessel.divisionMapping', 'divisionMapping')
          // .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
          // .leftJoin('vessel.vesselOwners', 'vesselOwnersPlans')
          .leftJoin('divisionMapping.division', 'division')
          .leftJoin('division.users', 'users')
          // .leftJoin(
          //   'vessel.vesselDocHolders',
          //   'vesselDocHolders',
          //   'vesselDocHolders.vesselId = vessel.id',
          //   // 'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now()',
          // )
          // .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
          // .addSelect([
          //   'vesselDocHolders.id',
          //   'vesselDocHolders.companyId',
          //   'vesselDocHolders.fromDate',
          //   'vesselDocHolders.toDate',
          //   'vesselDocHolders.responsiblePartyInspection',
          //   'vesselDocHolders.responsiblePartyQA',
          //   'vesselDocHolders.status',
          //   'companyVesselDocHolders.name',
          // ])
          .leftJoin(
            'vessel.vesselOwners',
            'vesselOwners',
            // 'vesselOwners.fromDate < now() and COALESCE(vesselOwners.toDate,now()) >= now()',
          )
          .leftJoin('vesselOwners.company', 'companyVesselOwnersPlans')
          .addSelect([
            'vesselOwners.id',
            'vesselOwners.companyId',
            'vesselOwners.fromDate',
            'vesselOwners.toDate',
            'vesselOwners.responsiblePartyInspection',
            'vesselOwners.responsiblePartyQA',
            'vesselOwners.status',
            'companyVesselOwnersPlans.name',
          ])
          .leftJoin(
            'vessel.vesselCharterers',
            'vesselCharterers',
            // 'vesselCharterers.fromDate < now() and COALESCE(vesselCharterers.toDate,now()) >= now()',
          )
          // .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
          .addSelect([
            'vesselCharterers.id',
            'vesselCharterers.companyId',
            'vesselCharterers.fromDate',
            'vesselCharterers.toDate',
            'vesselCharterers.responsiblePartyInspection',
            'vesselCharterers.responsiblePartyQA',
            'vesselCharterers.status',
            // 'companyVesselCharterers.name',
          ]);

        fieldSelects.push(
          'vesselOwners.id',
          'vesselOwners.companyId',
          'vesselOwners.fromDate',
          'vesselOwners.toDate',
          'vesselOwners.responsiblePartyInspection',
          'vesselOwners.responsiblePartyQA',
          'vesselOwners.status',
          'companyVesselOwnersPlans.name',
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
          // 'companyVesselCharterers.name',
        );
        await _supportCheckRoleScopeForGetList(
          this.manager,
          queryBuilder,
          user,
          whereForMainAndInternal,
          whereForExternal,
          'planningRequest',
          false,
          false,
          'workSpace',
        );
      }

      const connection = getConnection();
      const subQueryBuilder = connection.createQueryBuilder();

      this.buildSql(body, queryBuilder, user, subQueryBuilder, fieldSelects);

      const subQuerySelect = [];
      const queryBuilderNew = this._createQueryListAuditWorkspace();

      let dataList = await handleGetDataForAGGrid(
        this,
        queryBuilder,
        query,
        body,
        subQueryBuilder,
        queryBuilderNew,
        'workSpace',
        subQuerySelect,
      );

      if (dataList.data.length === 0) {
        return dataList;
      }
      if (body && isDoingGrouping(body)) {
        return dataList;
      }

      // const wsData = cloneDeep(dataList.data);

      // const prIds = wsData.map((ws) => ws.planningRequestId);

      // const prAuditTypes = await this.connection
      //   .getCustomRepository(PlanningRequestRepository)
      //   .createQueryBuilder('planningRequest')
      //   .leftJoin('planningRequest.auditTypes', 'auditTypes')
      //   .where('planningRequest.id IN (:...prIds)', { prIds })
      //   .select(['planningRequest.id'])
      //   .addSelect(['auditTypes.id', 'auditTypes.name'])
      //   .getMany();

      // for (const ws of wsData) {
      //   for (const pr of prAuditTypes) {
      //     if (ws.planningRequestId === pr.id) {
      //       ws.planningRequest.auditTypes = pr.auditTypes;
      //     }
      //   }
      // }

      // here checking the checklist is done by web or app logic
      // const modifiedWsData = wsData.map((item) => {
      //   item.fillAuditChecklists.some((checklist) => {
      //     if (checklist.webInstance) item['inspectionCarriedIn'] = checklistInstances.WEB;
      //   });
      //   item.fillAuditChecklists.some((checklist) => {
      //     if (checklist.appInstance) item['inspectionCarriedIn'] = checklistInstances.APP;
      //   });
      //   const checklistInstance = item.fillAuditChecklists.map((checklist) => {
      //     if (checklist.webInstance) return checklistInstances.WEB;
      //     if (checklist.appInstance) return checklistInstances.APP;
      //   });
      //   if (
      //     checklistInstance.includes(checklistInstances.WEB) &&
      //     checklistInstance.includes(checklistInstances.APP)
      //   )
      //     item['inspectionCarriedIn'] = checklistInstances.WEB_APP;
      //   delete item.fillAuditChecklists;

      //   return item;
      // });
      return {
        data: dataList?.data,
        page: dataList.page,
        pageSize: dataList.pageSize,
        totalPage: dataList.totalPage,
        totalItem: dataList.totalItem,
      };
    } catch (ex) {
      console.log(ex);
    }
  }

  async getWorkspaceGeneral(workspaceId: string, user: TokenPayloadModel) {
    // let whereCondition =
    //   '((workSpace.id = :id AND workSpace.companyId = :companyId AND :userId = ANY(workSpace.auditors)) OR (company.parentId = :companyId AND workSpace.id = :id))';
    // if (user.roleScope === RoleScope.ADMIN) {
    //   whereCondition =
    //     '((workSpace.id = :id AND workSpace.companyId = :companyId) OR (company.parentId = :companyId AND workSpace.id = :id))';
    // }
    let whereVesselDocHolder = `(1=1)`;
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselDocHolder = `(vesselDocHolders.responsiblePartyInspection = true AND vesselDocHolders.companyId = '${user.explicitCompanyId}' AND vesselDocHolders.deleted = false)`;
    }
    let whereVesselCharterer = `(1=1)`;
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselCharterer = `(vesselCharterers.responsiblePartyInspection = true AND vesselCharterers.companyId = '${user.explicitCompanyId}' AND vesselCharterers.deleted = false)`;
    }
    let whereVesselOwner = `(1=1)`;
    if (user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
      whereVesselOwner = `(vesselOwnersPlans.responsiblePartyInspection = true AND vesselOwnersPlans.companyId = '${user.explicitCompanyId}' AND vesselOwnersPlans.deleted = false)`;
    }
    const workSpace = await this.getOneQB(
      this.createQueryBuilder('workSpace')
        .leftJoinAndSelect('workSpace.vessel', 'vessel')
        .leftJoinAndSelect('workSpace.company', 'company')
        .leftJoinAndSelect('workSpace.planningRequest', 'planningRequest')
        .leftJoin('planningRequest.location', 'location')
        .leftJoin('planningRequest.auditors', 'auditors')
        .leftJoin('planningRequest.auditTypes', 'auditTypes')
        .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
        .leftJoin('planningRequest.fromPort', 'fromPort')
        .leftJoin('planningRequest.toPort', 'toPort')
        .leftJoin('workSpace.createdUser', 'createdUser')
        .leftJoin('workSpace.updatedUser', 'updatedUser')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin('planningRequest.auditCompany', 'auditCompany')
        .leftJoin('auditCompany.companyTypes', 'companyTypeAudit')
        //.leftJoin('planningRequest.department', 'department')
        .leftJoin('planningRequest.departments', 'departments')
        .leftJoin(
          'planningRequest.reportFindingForm',
          'reportFindingForm',
          'reportFindingForm.planningRequestId = planningRequest.id',
        )
        .leftJoin('reportFindingForm.internalAuditReport', 'internalAuditReport')
        .select()
        .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders', whereVesselDocHolder)
        .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
        .leftJoin('planningRequest.voyageType', 'voyageType')
        .addSelect([
          'vesselDocHolders.id',
          'vesselDocHolders.companyId',
          'vesselDocHolders.fromDate',
          'vesselDocHolders.toDate',
          'vesselDocHolders.responsiblePartyInspection',
          'vesselDocHolders.responsiblePartyQA',
          'vesselDocHolders.status',
          'companyVesselDocHolders.name',
          'voyageType.id',
          'voyageType.name',
        ])
        .leftJoin('vessel.vesselOwners', 'vesselOwnersPlans', whereVesselOwner)
        .leftJoin('vesselOwnersPlans.company', 'companyVesselOwnersPlans')
        .addSelect([
          'vesselOwnersPlans.id',
          'vesselOwnersPlans.companyId',
          'vesselOwnersPlans.fromDate',
          'vesselOwnersPlans.toDate',
          'vesselOwnersPlans.responsiblePartyInspection',
          'vesselOwnersPlans.responsiblePartyQA',
          'vesselOwnersPlans.status',
          'companyVesselOwnersPlans.name',
        ])
        .leftJoin('vessel.vesselCharterers', 'vesselCharterers', whereVesselCharterer)
        .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
        .addSelect([
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselCharterers.responsiblePartyInspection',
          'vesselCharterers.responsiblePartyQA',
          'vesselCharterers.status',
          'companyVesselCharterers.name',
        ])
        .addSelect([
          'createdUser.username',
          'updatedUser.username',
          'vesselType.name',
          'vesselType.icon',
          'auditors.id',
          'auditors.username',
          'auditTypes.id',
          'auditTypes.name',
          'leadAuditor.username',
          'fromPort.name',
          'toPort.name',
          'location.name',
          'reportFindingForm.id',
          'reportFindingForm.refNo',
          'reportFindingForm.sNo',
          'internalAuditReport.id',
          'internalAuditReport.refId',
          'internalAuditReport.serialNumber',
          'auditCompany.name',
          'auditCompany.companyIMO',
          'auditCompany.isCompanyRestricted',
          'departments.name',
          'companyTypeAudit.companyType',
          'auditTimeTable.actualFrom',
          'auditTimeTable.actualTo',
          'auditTimeTable.actualFrom_Year',
          'auditTimeTable.actualTo_Year',
          'auditTimeTable.actualFrom_Month',
          'auditTimeTable.actualTo_Month',
        ])
        .where('(workSpace.id = :id AND workSpace.companyId = :companyId)', {
          id: workspaceId,
          companyId: user.companyId,
          // userId: user.id,
        }),
    );

    // fetching the inspection mapping data
    if (workSpace?.inspectionMappingIds?.length) {
      const inspectionMapping = [];
      const { inspectionMappingIds } = workSpace;
      for (const inspectionMappingId of inspectionMappingIds) {
        const detailInspectionMapping = await this.connection
          .getCustomRepository(InspectionMappingRepository)
          .createQueryBuilder('inspectionMapping')
          .leftJoin('inspectionMapping.standardMasters', 'standardMasters')
          .where('inspectionMapping.id = :inspectionMappingId', {
            inspectionMappingId,
          })
          .select()
          .addSelect(['standardMasters'])
          .getOne();
        inspectionMapping.push(detailInspectionMapping); 
      }
      if (inspectionMapping?.length) {
        workSpace['inspectionMapping'] = inspectionMapping;
      }
    }

    if (workSpace) {
      if (!workSpace.seenUsers.includes(user.id)) {
        await this._appendSeenUser(workspaceId, user.id, workSpace.updatedAt);
      }
      return workSpace;
    } else {
      throw new BaseError({ status: 404, message: 'workspace.NOT_FOUND' });
    }
  }

  async addSignedROf(fileUpload: any, info: UploadDTO, user: TokenPayloadModel) {
    // update file pdf rof
    return 1;
  }

  private async _appendSeenUser(workspaceId: string, userId: string, currentUpdatedAt: Date) {
    return this.manager
      .createQueryBuilder()
      .update(AuditWorkspace)
      .set({
        seenUsers: () => {
          return `ARRAY_APPEND("seenUsers", '${userId}')`;
        },
        updatedAt: currentUpdatedAt,
      })
      .where('id = :workspaceId', {
        workspaceId,
      })
      .execute();
  }

  async listFindingSummaries(workSpaceId: string) {
    try {
      const workSpace = await this.connection
        .getRepository(AuditWorkspace)
        .createQueryBuilder('auditWorkspace')
        .where('auditWorkspace.id = :workSpaceId', { workSpaceId })
        .getOne();
      if (workSpace?.isSA) {
        const findingItems = await this.connection
          .getRepository(SAFindingItem)
          .createQueryBuilder('SAFindingItem')
          .leftJoin('SAFindingItem.elementMaster', 'elementMaster')
          .leftJoinAndSelect('SAFindingItem.fillSAChecklistQuestion', 'fillSAChecklistQuestion')
          .where('SAFindingItem.auditWorkspaceId = :workSpaceId', { workSpaceId })
          .addOrderBy('SAFindingItem.createdAt', 'ASC')
          .addSelect(['elementMaster.name', 'elementMaster.elementStageQ'])
          .getMany();
        return findingItems;
      } else {
        const findingItems = await this.connection
          .getRepository(ReportFindingItem)
          .createQueryBuilder('reportFindingItem')
          .leftJoin('reportFindingItem.chkQuestion', 'chkQuestion')
          .leftJoin('reportFindingItem.auditType', 'auditType')
          .leftJoin('reportFindingItem.natureFinding', 'natureFinding')
          .leftJoin('reportFindingItem.mainCategory', 'mainCategory')
          .leftJoin('reportFindingItem.secondCategory', 'secondCategory')
          .leftJoin('reportFindingItem.thirdCategory', 'thirdCategory')
          .leftJoin('reportFindingItem.auditChecklist', 'auditChecklist')
          .leftJoin('reportFindingItem.fillQuestion', 'fillQuestion')
          .leftJoin('reportFindingItem.observedRisk', 'observedRisk')
          .leftJoin('fillQuestion.fillAuditChecklist', 'fillAuditChecklist')
          .select()
          .addSelect([
            'auditChecklist.revisionNumber',
            'auditChecklist.code',
            'chkQuestion.id',
            'chkQuestion.code',
            'chkQuestion.question',
            'auditType.name',
            'natureFinding.name',
            'mainCategory.name',
            'secondCategory.name',
            'thirdCategory.name',
            'fillQuestion.id',
            'fillAuditChecklist.webInstance',
            'fillAuditChecklist.appInstance',
            'fillAuditChecklist.appInstance',
            'observedRisk.type',
          ])
          .where('reportFindingItem.auditWorkspaceId = :workSpaceId', { workSpaceId })
          .addOrderBy('reportFindingItem.createdAt', 'ASC')
          .getMany();

        // const findingItemWithRefCate = [];
        const seenIds = new Set();
        const uniqueFindingSummaries = [];
        const observedRisks = await this.manager.getCustomRepository(ObservedRiskRepository).find();
        for (let i = 0; i < findingItems.length; i++) {
          const item = { ...findingItems[i], ref: null };
          if (item.chkQuestionId) {
            const objRefCate = await this.manager
              .getCustomRepository(ChkQuestionRepository)
              .detailQuestionRefCategory(item.chkQuestionId);

            item.ref = objRefCate;

            if (!item.observedRiskId && objRefCate?.potential_risk) {
              const observedRiskMapping = observedRisks.find(
                (ob) => ob.type === objRefCate?.potential_risk,
              );
              item.observedRiskId = observedRiskMapping?.id;
              item.observedRisk = observedRiskMapping;
            }
          }
          // to avoid the duplicate findings
          // if (item.fillQuestion || !item.chkQuestion) {
          //   findingItemWithRefCate.push(item);
          // }
          if (!item.chkQuestion) {
            uniqueFindingSummaries.push(item);
          } else if (item?.chkQuestion?.id) {
            const key = item.chkQuestion?.id;

            if (!seenIds.has(key)) {
              seenIds.add(key);
              uniqueFindingSummaries.push(item);
            }
          }
        }

        return uniqueFindingSummaries;
      }
    } catch (ex) {
      throw ex;
    }
  }

  async submitFinalWorkspace(user: TokenPayloadModel, auditWorkspaceId: string, body: TimezoneDTO) {
    try {
      const workspace = await this.createQueryBuilder('workspace')
        .leftJoin('workspace.fillAuditChecklists', 'fillAuditChecklist')
        .select()
        .addSelect(['fillAuditChecklist.status'])
        .where('(workspace.id = :auditWorkspaceId)', { auditWorkspaceId })
        .getOne();
      const notFinalChecklists = workspace.fillAuditChecklists.find(
        (checklist) => checklist.status !== FillAuditChecklistStatus.COMPLETED,
      );
      if (notFinalChecklists) {
        throw new BaseError({ status: 404, message: 'workspace.CHECKLISTS_NOT_FINAL' });
      }

      if (workspace.status === AuditWorkspaceStatus.FINAL) {
        throw new BaseError({ status: 404, message: 'workspace.FINAL_ALREADY' });
      }

      // update workspace status and trigger ROF
      const dataSendMail: IEmailEventModel[] = [];
      const dataNoti: INotificationEventModel[] = [];
      return await this.connection.transaction(async (manager) => {
        // prepare ROF items to trigger ROF form
        const rofItems = await manager
          .getCustomRepository(ReportFindingItemRepository)
          .listFindingItemsByPR(workspace.planningRequestId, user);

        // delete workSpaceId to create ReportFinding Form
        for (let i = 0; i < rofItems.length; i++) {
          const rofItem = rofItems[i];
          delete rofItem.auditWorkspaceId;
          delete rofItem.auditType;
          delete rofItem.natureFinding;
        }

        //  prepare ROF form data
        const rofForm: CreateReportFindingFormDto = {
          planningRequestId: workspace.planningRequestId,
          timezone: body.timezone,
          reportFindingItems: rofItems,
          previousNCFindings: [],
          officeComment: '',
          comments: [],
          workflowRemark: '',
          isSubmit: false,
        };
        // Get user info
        const createdUser = await manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);

        // Extracting month and year column
        const monthList = Object.keys(SelfAssessmentMonthEnum).map(
          (key) => SelfAssessmentMonthEnum[key],
        );
        const submittedDate = new Date();
        const submittedDateMonth = submittedDate.getMonth();
        const finalSubmittedDateMonth = monthList[submittedDateMonth];
        const submittedDateYear = submittedDate.getFullYear();
        // change status of workspace if all check list is COMPLETED
        await manager.update(
          AuditWorkspace,
          { id: auditWorkspaceId },
          {
            status: AuditWorkspaceStatus.FINAL,
            submittedDate: submittedDate,
            submittedDate_Month: finalSubmittedDateMonth,
            submittedDate_Year: submittedDateYear,
          },
        );
        // send notify when change status record workspace
        if (workspace.status === AuditWorkspaceStatus.DRAFT) {
          if (workspace.auditors && workspace.auditors.length > 0) {
            const listReceiverNoti = await manager
              .getCustomRepository(UserRepository)
              .listByIds(workspace.auditors);
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti as IUser[],
              module: ModuleType.AUDIT_WORKSPACE,
              recordId: auditWorkspaceId,
              recordRef: workspace.refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: 'Submitted',
              previousStatus: 'In progress',
              performer: performer,
              executedAt: new Date(),
            });

            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: workspace.refNo,
                  recordId: auditWorkspaceId,
                  path: ModulePathEnum.AUDIT_INSPECTION_WORKSPACE,
                  currentStatus: 'Submitted',
                  previousStatus: 'In progress',
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
        }
        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_WORKSPACE,
            planningId: workspace.planningRequestId,
          },
          user,
          [AuditActivityEnum.COMPLETED],
          createdUser,
        );

        // Trigger PR to In Progress
        await manager
          .getCustomRepository(PlanningRequestRepository)
          ._triggerInProgressPlanningRequest(workspace.planningRequestId, user.id, createdUser);

        //  create ROF form
        await manager
          .getCustomRepository(ReportFindingFormRepository)
          ._createReportFindingFormHelper(manager, rofForm, user, createdUser);
        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      throw ex;
    }
  }

  async updateShowPopupAnalyticalReport(
    user: TokenPayloadModel,
    auditWorkspaceId: string,
    body: UpdateShowPopupAnalyticalReportDto,
  ) {
    try {
      const result = await this.manager.update(
        AuditWorkspace,
        {
          id: auditWorkspaceId,
          companyId: user.companyId,
        },
        {
          showPopupAnalyticalReport: body.showPopupAnalyticalReport,
        },
      );
      if (result.affected === 0) {
        throw new BaseError({ status: 404, message: 'workspace.NOT_FOUND' });
      }
      return 1;
    } catch (ex) {
      throw ex;
    }
  }

  async updateMasterOrChiefOfEngineer(
    user: TokenPayloadModel,
    auditWorkspaceId: string,
    body: UpdateMasterOrChiefOfEngineerDto,
  ) {
    try {
      const workSpace = await this.getOneQB(
        this.createQueryBuilder('workSpace')
          .select()
          .where('(workSpace.id = :id AND workSpace.companyId = :companyId)', {
            id: auditWorkspaceId,
            companyId: user.companyId,
          }),
      );

      if (workSpace?.entityType === AuditEntity.OFFICE) {
        throw new BaseError({
          status: 400,
          message: 'workspace.ERR_UPDATE_MASTER_AND_CHIEF_ENTIY_OFFICE',
        });
      }
      const result = await this.manager.update(
        AuditWorkspace,
        {
          id: auditWorkspaceId,
          companyId: user.companyId,
        },
        {
          [body.type]: body.value,
        },
      );
      if (result.affected === 0) {
        throw new BaseError({ status: 404, message: 'workspace.NOT_FOUND' });
      }
      return 1;
    } catch (ex) {
      throw ex;
    }
  }

  async _initFillAuditChecklistAndQuestions(
    manager: EntityManager,
    workspaceId: string,
    auditChecklists: AuditChecklist[],
  ) {
    try {
      const preparedFillChecklists = [];
      const preparedFillChecklistQuestions = [];
      for (let i = 0; i < auditChecklists.length; i++) {
        //loop through each audit type of check-list to create fill-checklist
        for (let j = 0; j < auditChecklists[i].inspectionMappings.length; j++) {
          const checkListId = auditChecklists[i].id;
          const fillChecklistId = Utils.strings.generateUUID();

          //  Create fill-checklist for each audit-type
          const auditTypeId = auditChecklists[i].inspectionMappings[j].auditType.id;
          preparedFillChecklists.push({
            id: fillChecklistId,
            auditChecklistId: checkListId,
            auditWorkspaceId: workspaceId,
            inspectionMappingId: auditChecklists[i].inspectionMappings[j].id,
            status: FillAuditChecklistStatus.YET_TO_START,
            auditTypeId: auditTypeId,
            updatedAt: null,
          });

          // Build fill checklist questions
          preparedFillChecklistQuestions.push(
            ...auditChecklists[i].questions.map((q) => ({
              fillAuditChecklistId: fillChecklistId,
              chkQuestionId: q.id,
              locationId: q.locationId,
            })),
          );
        }
      }
      await manager.insert(FillAuditChecklist, preparedFillChecklists);
      await manager.insert(FillAuditChecklistQuestion, preparedFillChecklistQuestions);
    } catch (ex) {
      throw ex;
    }
  }

  async _initFillSAChecklistAndQuestions(
    manager: EntityManager,
    workspaceId: string,
    saStandardChecklists: SelfAssessment[],
  ) {
    try {
      const preparedFillChecklists = [];
      const preparedFillChecklistQuestions = [];
      for (let i = 0; i < saStandardChecklists?.length; i++) {
        //loop through each audit type of check-list to create fill-checklist
        for (let j = 0; j < saStandardChecklists[i]?.standardMaster?.inspectionMapping?.length; j++) {
          const checkListId = saStandardChecklists[i]?.id;
          const fillChecklistId = Utils.strings.generateUUID();

          //  Create fill-checklist for each audit-type
          const inspectionMapping = saStandardChecklists[i]?.standardMaster?.inspectionMapping[j];
          const auditTypeId = inspectionMapping?.auditType?.id;
          preparedFillChecklists.push({
            id: fillChecklistId,
            auditChecklistId: null,
            selfAssessmentId: checkListId,
            auditWorkspaceId: workspaceId,
            inspectionMappingId: inspectionMapping?.id,
            status: FillAuditChecklistStatus.YET_TO_START,
            auditTypeId: auditTypeId,
          });

          preparedFillChecklistQuestions.push(
            ...saStandardChecklists[i].selfDeclarations?.map((selfDeclaration) => ({
              fillAuditChecklistId: fillChecklistId,
              standardMasterId: saStandardChecklists[i]?.standardMaster?.id,
              elementMasterId: selfDeclaration?.elementMaster?.id,
              selfDeclarationId: selfDeclaration?.id,
              complianceId: selfDeclaration?.compliance?.id,
              sAQuestionsId: selfDeclaration?.elementMaster?.id,
              selfDeclarationCommentIds: selfDeclaration?.selfDeclarationComments.map(
                (comment) => comment.id,
              ),
              standardName: saStandardChecklists[i]?.standardMaster?.name,
              assessmentCompliance: selfDeclaration?.compliance?.answer,
              selfDeclarationComments: selfDeclaration?.selfDeclarationComments.map(
                (comment) => comment.comment,
              ),
              stage: selfDeclaration?.elementMaster?.stage,
              elementCode: selfDeclaration?.elementMaster?.code,
              elementNumber: selfDeclaration?.elementMaster?.number,
              keyPerformanceIndicator: selfDeclaration?.elementMaster?.keyPerformanceIndicator,
              bestPracticeGuidance: selfDeclaration?.elementMaster?.bestPracticeGuidance,
              auditorCompliance: null,
            })),
          );
        }
      }
      await manager.insert(FillAuditChecklist, preparedFillChecklists);
      await manager.insert(FillSAChecklistQuestion, preparedFillChecklistQuestions);
    } catch (ex) {
      throw ex;
    }
  }

  private _genWorkspaceSerialCode(
    counter: number,
    currYear: number,
    currMonth: number,
    vesselsCode: string,
  ) {
    return `IW${vesselsCode + currMonth + currYear + counter}`;
  }

  async _updateStatusWorkspaceMobile(
    auditWorkspaceId: string,
    workspaceStatus: string,
    userId: string,
    InspectionstartDate?: string,
    InspectionEndDate?: string,
    hasFindings?: boolean,
  ) {
    const workspace = await this.createQueryBuilder('aw')
      .leftJoin('aw.vessel', 'vessel')
      .leftJoin('aw.company', 'company')
      .select()
      .addSelect(['vessel.code', 'company.id', 'company.code'])
      .where('aw.id = :auditWorkspaceId', { auditWorkspaceId })
      .getOne();

    // create refId

    return await this.connection.transaction(async (manager) => {
      if (!workspace.refNo) {
        const awsRefNoCounter = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager: manager,
            companyId: workspace.company.id,
            feature: FeatureVersionConfig.AUDIT_INSPECTION_WORKSPACE_REF_NO,
            year: new Date().getFullYear(),
          });

        await manager.update(
          AuditWorkspace,
          { id: auditWorkspaceId },
          {
            isNew: false,
            refNo: this._genWorkspaceRefNo(
              awsRefNoCounter,
              new Date().getFullYear(),
              workspace.company.code,
            ),
            status: AuditWorkspaceStatus.DRAFT,
          },
        );
      }

      // trigger workspace status to DRAFT if exist checklist status equal COMPLETED
      // if (workspaceStatus === 'F') {
      //   await manager.update(
      //     AuditWorkspace,
      //     { id: auditWorkspaceId },
      //     {
      //       status: AuditWorkspaceStatus.FINAL,
      //       updatedUserId: userId,
      //       mobileInspectionStartDate: InspectionstartDate,
      //       mobileInspectionEndDate: InspectionEndDate,
      //     },
      //   );
      // // send notify when change status record workspace
      // if (workspace.status === AuditWorkspaceStatus.DRAFT) {
      //   if (workspace.auditors && workspace.auditors.length > 0) {
      //     const listReceiverNoti: IUser[] = await manager
      //       .getCustomRepository(UserRepository)
      //       .listByIds(workspace.auditors);
      //     const performer = await manager
      //       .getCustomRepository(UserRepository)
      //       .getUserDetailAndSelect(userId, ['id', 'username', 'jobTitle']);
      //     dataNoti.push({
      //       receivers: listReceiverNoti,
      //       module: ModuleType.AUDIT_CHECKLIST,
      //       recordId: auditWorkspaceId,
      //       recordRef: workspace.refNo,
      //       type: PushTypeEnum.CHANGE_RECORD_STATUS,
      //       currentStatus: 'Submitted',
      //       previousStatus: 'In progress',
      //       performer: performer,
      //       executedAt: new Date(),
      //     });
      //   }
      // }
      // Handle add Audit log
      //   await manager.getCustomRepository(AuditLogRepository).createAuditLog(
      //     {
      //       module: AuditModuleEnum.INSPECTION_WORKSPACE,
      //       planningId: workspace.planningRequestId,
      //     },
      //     { id: userId } as TokenPayloadModel,
      //     [AuditActivityEnum.COMPLETED],
      //     null,
      //   );
      // } else {
      await manager.update(
        AuditWorkspace,
        { id: auditWorkspaceId },
        {
          status: AuditWorkspaceStatus.DRAFT,
          updatedUserId: userId,
          mobileInspectionStartDate: InspectionstartDate,
          mobileInspectionEndDate: InspectionEndDate,
          isGenerateROF: hasFindings ? true : false,
          submittedDate: new Date(),
        },
      );

      // Handle add Audit log
      await manager.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.INSPECTION_WORKSPACE,
          planningId: workspace.planningRequestId,
        },
        { id: userId } as TokenPayloadModel,
        [AuditActivityEnum.UPDATED_INFO],
        null,
      );
      // }
    });
  }

  private _genWorkspaceRefNo(counter: number, currYear: number, companyCode: string) {
    return `${companyCode}/IW/${counter}/${currYear}`;
  }

  async getSummaryData(workspaceId: string, user: TokenPayloadModel) {
    const data = await this.createQueryBuilder('auditWorkspace')
      .leftJoin('auditWorkspace.vessel', 'vessel')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('auditWorkspace.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      //.leftJoin('planningRequest.department', 'department')
      .leftJoin('planningRequest.departments', 'departments')
      .leftJoin('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.followUp', 'followUp')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .leftJoin('planningRequest.internalAuditReport', 'internalAuditReport')
      .leftJoin('internalAuditReport.internalAuditReportHistories', 'internalAuditReportHistories')
      .leftJoin('planningRequest.reportFindingForm', 'reportFindingForm')
      .leftJoin('reportFindingForm.reportFindingHistories', 'reportFindingHistories')
      .leftJoin(
        'reportFindingForm.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .leftJoin('reportFindingItems.auditType', 'auditType')
      .leftJoin('planningRequest.cars', 'cars')
      .leftJoin('cars.cap', 'cap')
      .leftJoin('cars.cARVerification', 'cARVerification')
      .select([
        'auditWorkspace.id',
        'auditWorkspace.master',
        'auditWorkspace.chiefOfEngineer',
        'vessel.id',
        'vessel.name',
        'vesselType.name',
        'vessel.buildDate',
        'planningRequest.id',
        'planningRequest.workingType',
        'planningRequest.typeOfAudit',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.status',
        'departments.id',
        'departments.name',
        'planningRequest.memo',
        'auditors.username',
        'fromPort.name',
        'toPort.name',
        'auditTimeTable.actualFrom',
        'internalAuditReport.id',
        'internalAuditReport.status',
        'internalAuditReportHistories.createdAt',
        'internalAuditReportHistories.status',
        'reportFindingForm.id',
        'reportFindingForm.status',
        'reportFindingHistories.createdAt',
        'reportFindingHistories.status',
        'cars',
        'cap',
        'cARVerification',
        'reportFindingItems.id',
        'auditType.name',
        'followUp.id',
      ])
      .where('auditWorkspace.id = :workspaceId', { workspaceId })
      .getOne();

    return data;
  }

  buildSql(body: PayloadAGGridDto, queryBuilder, token, subQueryBuilder, fieldSelects?: string[]) {
    if (body) {
      convertFilterField(body, AUDIT_WORKSPACE_FILTER_FIELDS);

      // const queryDocHolder = this.connection
      //   .getCustomRepository(PlanningRequestRepository)
      //   .createQueryBuilder('planningRequest')
      //   .leftJoin('planningRequest.auditCompany', 'auditCompany')
      //   .leftJoin('planningRequest.vessel', 'vessel')
      //   .leftJoin(
      //     'vessel.vesselDocHolders',
      //     'vesselDocHolders',
      //     `vesselDocHolders.status = 'active'`,
      //   )
      //   .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      //   .select('planningRequest.id')
      //   .addSelect([
      //     `CASE WHEN "planningRequest"."entityType" = 'Office'
      //               THEN "auditCompany"."name" ELSE STRING_AGG(DISTINCT "companyVesselDocHolders"."name", ', ')
      //               END AS "holderCompany"`,
      //     `"auditCompany"."name" AS "auditCompany_name"`,
      //   ])
      //   .groupBy(`planningRequest.id, auditCompany.name`);

      // const queryAuditTypes = this.manager
      //   .getCustomRepository(PlanningRequestRepository)
      //   .createQueryBuilder('planningRequest')
      //   .leftJoin('planningRequest.auditTypes', 'auditTypes')
      //   .select('planningRequest.id')
      //   .addSelect(`STRING_AGG(DISTINCT auditTypes.name, ', ') AS "auditTypesName"`)
      //   .groupBy(`planningRequest.id`);

      // const queryCarriers = this.createQueryBuilder('workSpace')
      //   .leftJoin('workSpace.fillAuditChecklists', 'fillAuditChecklists')
      //   .select('workSpace.id')
      //   .addSelect([
      //     `(  case 
      //           when (
      //                 SUM( case when  "fillAuditChecklists"."appInstance" is not null then 1 else 0 end) > 0 
      //             AND SUM( case when  "fillAuditChecklists"."webInstance" is not null then 1 else 0 end) > 0 ) 
      //             then '${checklistInstances.WEB_APP}'
      //           when   SUM( case when  "fillAuditChecklists"."appInstance" is not null then 1 else 0 end) > 0 then '${checklistInstances.APP}'
      //           when   SUM( case when  "fillAuditChecklists"."webInstance" is not null then 1 else 0 end) > 0 then '${checklistInstances.WEB}' 
      //           END) as "inspectionCarriedIn" `,
      //   ])
      //   .groupBy(`workSpace.id`);

      // queryBuilder
      //   .innerJoin(
      //     `(${queryDocHolder.getQuery()})`,
      //     'holderCompanyGroup',
      //     `"holderCompanyGroup"."planningRequest_id" = "planningRequest"."id"`,
      //   )
      //   .innerJoin(
      //     `(${queryAuditTypes.getQuery()})`,
      //     'auditTypesGroup',
      //     `"auditTypesGroup"."planningRequest_id" = "planningRequest"."id"`,
      //   )
      //   .innerJoin(
      //     `(${queryCarriers.getQuery()})`,
      //     'carrierGroups',
      //     `"carrierGroups"."workSpace_id" = "workSpace"."id"`,
      //   );

      queryBuilder
        // .addSelect(`"auditTypesGroup"."auditTypesName"`)
        // .addSelect(`"carrierGroups"."inspectionCarriedIn"`)
        // .addSelect(`"holderCompanyGroup"."holderCompany"`)
        // .addSelect(`"holderCompanyGroup"."auditCompany_name"`)
        .andWhere(`workSpace.deleted = false`)
        .groupBy(
          `workSpace.id, 
          "planningRequest"."id",
          ${fieldSelects.join(', ')}`,
        )
        .distinctOn(['workSpace.id']);

      subQueryBuilder
        .select(`DISTINCT "workSpace_id" AS id`)
        .from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"workSpace_id"');
    }

    queryBuilder.groupBy();
  }

  async _isGenerateROF(auditWorkspaceId: string) {
    return await this.createQueryBuilder('workSpace')
      .where('workSpace.id = :auditWorkspaceId', { auditWorkspaceId })
      .andWhere('workSpace.isGenerateROF = false')
      .getOne();
  }
}
