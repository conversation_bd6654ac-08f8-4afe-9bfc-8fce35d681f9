import { isEmpty, omit, pick } from 'lodash';
import { arrayToStringCustom, decryptAttachmentValues } from 'src/commons/functions';
import { BaseError, TokenPayloadModel, TypeORMRepository, Utils } from 'svm-nest-lib-v3';
import { Connection, EntityRepository, In, IsNull, Not, getCustomRepository } from 'typeorm';
import { AppConst } from '../../../commons/consts/app.const';
import {
  ActionValueChangeEnum,
  AuditChkQuestionType,
  AuditEntity,
  AuditWorkspaceStatus,
  EmailTypeEnum,
  FillAuditChecklistStatus,
  MailTemplate,
  ModuleEnum,
  ModulePathEnum,
  PushTypeEnum,
  RemarkType,
} from '../../../commons/enums';
import { commonCheckValueChange } from '../../../commons/functions/value-change-history';
import APP_CONFIG from '../../../configs/app.config';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  IUser,
  NotificationProducer,
} from '../../../micro-services/async/notification.producer';
import { MySet } from '../../../utils';
import { ChkQuestionRepository } from '../../audit-checklist/repository/chk-question.repository';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { MasterTableObj } from '../../commons/master-table/data/master-table.data';
import { InternalAuditReportRepository } from '../../internal-audit-report/repositories/internal-audit-report.repository';
import { PlanningRequestRepository } from '../../planning-request/repositories/planning-request.repository';
import { CreateReportFindingFormDto } from '../../report-finding/dto';
import { ReportFindingForm } from '../../report-finding/entities/report-finding-form.entity';
import { ReportFindingFormRepository } from '../../report-finding/repositories/report-finding-form.repository';
import { ModuleType } from '../../user-assignment/user-assignment.enum';
import { UserRepository } from '../../user/user.repository';
import { ValueChangeHistory } from '../../value-change-history/value-change-history.entity';
import { ListFillChecklistDetailDto } from '../dto';
import { FillChecklistQuestionDto } from '../dto/create-fill-audit-checklist-question.dto';
import { FillChecklistQuickAnswersDto } from '../dto/update-fill-checklist-quick-answer.dto';
import { UpdateFillAuditChecklistDto } from '../dto/update-fill-question.dto';
import { AuditWorkspace } from '../entities/audit-workspace.entity';
import { FillAuditChecklistQuestion } from '../entities/fill-audit-checklist-question.entity';
import { FillAuditChecklist } from '../entities/fill-audit-checklist.entity';
import { OnboardFindingItem } from '../entities/onboard-finding-item.entity';
import { ReportFindingItem } from '../entities/report-finding-item.entity';
import {
  FilterListFillCheckListAnsweredEnum,
  FilterListFillCheckListMandatoryFieldsEnum,
  FilterListFillCheckListQuestionTypeEnum,
} from '../enums';
import { AuditWorkspaceRepository } from './audit-workspace.repository';
import { FillAuditChecklistRepository } from './fill-audit-checklist.repository';
import { ReportFindingItemRepository } from './report-finding-item.repository';
import { CorrectiveActionRequest } from 'src/modules/corrective-action-request/entities/car.entity';
import { InternalAuditReport } from 'src/modules/internal-audit-report/entities/internal-audit-report.entity';
import { CARRepository } from 'src/modules/corrective-action-request/repositories/car.repository';
import { MetaConfig } from 'src/modules-qa/catalog/entity/meta-config.entity';
import { CatalogConst } from 'src/modules-qa/catalog/catalog-key.const';
import { SAFindingItem } from '../entities/sa-finding-items.entity';
const _ = require('lodash');
@EntityRepository(FillAuditChecklistQuestion)
export class FillAuditChecklistQuestionRepository extends TypeORMRepository<FillAuditChecklistQuestion> {
  constructor(
    private readonly connection: Connection,
    private readonly notificationProducer: NotificationProducer,
  ) {
    super();
  }

  async _migrateSyncAttachmentToFindingAndCar() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_SYNC_ATTACHMENT,
      })
      .getOne();
    if (!metaConfig) {
      return await this.connection.transaction(async (managerTrans) => {
        const findingInFillQuestion = await managerTrans
          .getCustomRepository(FillAuditChecklistRepository)
          .getAttByFindingItemIds([]);

        const listFindingIds = Array.from(findingInFillQuestion.keys());
        const findingFound = await managerTrans
          .getCustomRepository(ReportFindingItemRepository)
          .findByIds(listFindingIds);
        for (const finding of findingFound) {
          const attFillQuestion = decryptAttachmentValues(findingInFillQuestion.get(finding.id));
          const attFinding = decryptAttachmentValues(finding.findingAttachments);
          const findingAttachment = Array.from(new Set([...attFillQuestion, ...attFinding]));

          if (finding.carId) {
            const carAtt = (
              await managerTrans.getCustomRepository(CARRepository).findOne(finding.carId)
            ).attachments;
            const attachmentSyncToCAR = Array.from(
              new Set([...decryptAttachmentValues(carAtt), ...findingAttachment]),
            );
            await managerTrans
              .getCustomRepository(CARRepository)
              .updateCarAttachment(finding.carId, attachmentSyncToCAR);
          }

          await managerTrans
            .getCustomRepository(FillAuditChecklistQuestionRepository)
            .updateReportFinding(finding.id, findingAttachment);
        }
        await managerTrans.save(MetaConfig, {
          key: CatalogConst.MIGRATE_SYNC_ATTACHMENT,
          lastTimeSync: '2024-03-12T14:20:00.000z',
        });
      });
    }
  }

  async listFillQuestionsOfFillChecklist(
    fillCheckListId: string,
    user: TokenPayloadModel,
    workSpaceId?: string,
  ) {
    try {
      const response = await this._supportGetFillCheckList(fillCheckListId, workSpaceId, user);
      return response.fillAuditChecklists;
    } catch (ex) {
      throw ex;
    }
  }

  async checkCompleteChecklist(
    fillCheckListId: string,
    user: TokenPayloadModel,
    workSpaceId?: string,
  ) {
    try {
      const response = await this._supportGetFillCheckList(
        fillCheckListId,
        workSpaceId,
        user,
        true,
      );
      const result = response.fillAuditChecklists;
      // check filter has answer complete
      for (const quest of result) {
        if (quest.chkQuestion.isMandatory && quest.answers.length === 0) {
          return { checkComplete: false };
        } else {
          if (quest.answers?.length > 0) {
            if (quest.chkQuestion.hasRemark) {
              if (!this._checkRemark(quest)) {
                return { checkComplete: false };
              }
            }
          }
        }
        if (quest.chkQuestion.requireEvidencePicture && quest.evidencePictures.length < 1) {
          return { checkComplete: false };
        }
        if (quest.attachments.length < quest.chkQuestion.minPictureRequired) {
          return { checkComplete: false };
        }
      }
      return { checkComplete: true };
    } catch (ex) {
      throw ex;
    }
  }

  async listFillQuestionsOfFillChecklistGetReferenceData(
    query: ListFillChecklistDetailDto,
    fillCheckListId: string,
    user: TokenPayloadModel,
    workSpaceId?: string,
  ) {
    try {
      const response = await this._supportGetFillCheckList(
        fillCheckListId,
        workSpaceId,
        user,
        false,
        false,
        true,
        query,
      );
      const result = response.fillAuditChecklists;
      const fillChecklistDetail = response.fillChecklistDetail;

      // Group fill questions by topic
      const topicMap = {};
      for (let i = 0; i < result.length; i++) {
        const quest = result[i];
        const topic = quest.chkQuestion.topic.name;
        if (!topicMap[topic]) {
          topicMap[topic] = [];
        }

        topicMap[topic].push(quest);
      }

      const res = [];
      // Transform map to Array
      const topics = Object.keys(topicMap);
      for (let i = 0; i < topics.length; i++) {
        res.push({
          topic: topics[i],
          questions: topicMap[topics[i]],
        });
      }
      const pageSize = query.pageSize || query.pageSize > 0 ? query.pageSize : 20;
      const pageNumber = query.page || query.page > 1 ? query.page : 1;
      const inspectionMappingId = fillChecklistDetail.inspectionMappingId;
      for (const data of res) {
        const dataQuestions = this._paginate(data.questions, pageSize, pageNumber);
        for (const question of dataQuestions) {
          const inspectionMappings = question.chkQuestion.auditChecklist.inspectionMappings;
          const inspectionMappingInfo = inspectionMappings.find(
            (mapping) => mapping.id === inspectionMappingId,
          );
          if (!question.reportFindingItem) {
            question.reportFindingItem = {};
            Object.assign(question.reportFindingItem, {
              natureFindingId: inspectionMappingInfo
                ? inspectionMappingInfo.natureFindings.natureFindingId
                : null,
              auditTypeId: inspectionMappingInfo ? inspectionMappingInfo.auditTypeId : null,
            });
          }
          const referenceData = await this.manager
            .getCustomRepository(ChkQuestionRepository)
            .detailQuestionRefCategory(question.chkQuestionId);
          Object.assign(question, { referenceData: referenceData });
        }
        Object.assign(data, {
          questions: dataQuestions,
          page: pageNumber,
          pageSize: pageSize,
          totalItem: data.questions.length,
          totalPage: Math.ceil(data.questions.length / pageSize),
        });
      }

      return res;
    } catch (ex) {
      throw ex;
    }
  }

  async getGroupQuestions(
    query: ListFillChecklistDetailDto,
    fillCheckListId: string,
    user: TokenPayloadModel,
    workSpaceId?: string,
  ) {
    try {
      const response = await this._supportGetFillCheckList(
        fillCheckListId,
        workSpaceId,
        user,
        false,
        true,
        false,
        query,
      );
      const result: FillAuditChecklistQuestion[] = response.fillAuditChecklists;
      const questionTypeYesNo = [];
      const questionTypeYesNoNa = [];
      const questionTypeYesNsNa = [];
      const questionTypeComboList = {};
      const questionTypeRadioList = {};
      const questionTypeOther = {};

      result?.forEach((q) => {
        const answerString = q.chkQuestion.answerOptions
          ?.map((an) => an.content)
          .sort()
          .join();

        switch (q.chkQuestion?.type) {
          case AuditChkQuestionType.YES_NO: {
            questionTypeYesNo.push(q);
            break;
          }
          case AuditChkQuestionType.YES_NO_NA: {
            questionTypeYesNoNa.push(q);

            break;
          }
          case AuditChkQuestionType.YES_NO_NS_NA: {
            questionTypeYesNsNa.push(q);
            break;
          }
          case AuditChkQuestionType.COMBO_LIST: {
            if (answerString in questionTypeComboList) {
              questionTypeComboList[answerString] = [...questionTypeComboList[answerString], q];
            } else {
              questionTypeComboList[answerString] = [q];
            }
            break;
          }
          case AuditChkQuestionType.RADIO_LIST: {
            if (answerString in questionTypeRadioList) {
              questionTypeRadioList[answerString] = [...questionTypeRadioList[answerString], q];
            } else {
              questionTypeRadioList[answerString] = [q];
            }
            break;
          }
          default: {
            if (answerString in questionTypeOther) {
              questionTypeOther[answerString] = [...questionTypeOther[answerString], q];
            } else {
              questionTypeOther[answerString] = [q];
            }
          }
        }
      });
      const res = [];
      if (questionTypeYesNo.length) {
        res.push({
          type: AuditChkQuestionType.YES_NO,
          answerOptions: questionTypeYesNo[0].chkQuestion.answerOptions.map((q) => q.content),
          questions: questionTypeYesNo,
        });
      }
      if (questionTypeYesNoNa.length) {
        res.push({
          type: AuditChkQuestionType.YES_NO_NA,
          answerOptions: questionTypeYesNoNa[0].chkQuestion.answerOptions.map((q) => q.content),
          questions: questionTypeYesNoNa,
        });
      }

      if (questionTypeYesNsNa.length) {
        res.push({
          type: AuditChkQuestionType.YES_NO_NS_NA,
          answerOptions: questionTypeYesNsNa[0].chkQuestion.answerOptions.map((q) => q.content),
          questions: questionTypeYesNsNa,
        });
      }

      if (!isEmpty(questionTypeComboList)) {
        for (const [, value] of Object.entries(questionTypeComboList)) {
          const questions = value as any[];
          res.push({
            type: AuditChkQuestionType.COMBO_LIST,
            answerOptions: questions[0].chkQuestion.answerOptions.map((q) => q.content),
            questions: questions,
          });
        }
      }

      if (!isEmpty(questionTypeRadioList)) {
        for (const [, value] of Object.entries(questionTypeRadioList)) {
          const questions = value as any[];
          res.push({
            type: AuditChkQuestionType.RADIO_LIST,
            answerOptions: questions[0].chkQuestion.answerOptions.map((q) => q.content),
            questions: questions,
          });
        }
      }

      if (!isEmpty(questionTypeOther)) {
        for (const [, value] of Object.entries(questionTypeRadioList)) {
          const questions = value as any[];
          res.push({
            type: 'Other',
            answerOptions: questions[0].chkQuestion.answerOptions.map((q) => q.content),
            questions: questions,
          });
        }
      }

      const pageSize = query.pageSize || query.pageSize > 0 ? query.pageSize : 20;
      const pageNumber = query.page || query.page > 1 ? query.page : 1;
      const dataQuestions = this._paginate(res || [], pageSize, pageNumber);
      return {
        questions: dataQuestions,
        page: pageNumber,
        pageSize: pageSize,
        totalItem: dataQuestions.length,
        totalPage: Math.ceil(dataQuestions.length / pageSize),
      };
    } catch (ex) {
      throw ex;
    }
  }

  _checkRemark(quest: FillAuditChecklistQuestion) {
    let result = false;
    if (quest.chkQuestion.hasRemark) {
      const answerId = quest.answers[0];
      if (
        ((quest.chkQuestion.hasRemark === RemarkType.ALL ||
          (quest.chkQuestion.hasRemark === RemarkType.SPECIFIC &&
            quest.chkQuestion.remarkSpecificAnswers.indexOf(answerId) > -1)) &&
          quest.findingRemark !== null &&
          quest.findingRemark !== '') ||
        (quest.chkQuestion.hasRemark === RemarkType.SPECIFIC &&
          quest.chkQuestion.remarkSpecificAnswers.indexOf(answerId) === -1)
      ) {
        result = true;
      }
    }
    return result;
  }
  _paginate(array, pageSize, pageNumber) {
    return pageSize < 0 ? array : array.slice((pageNumber - 1) * pageSize, pageNumber * pageSize);
  }

  async completedCheckListDetail(
    fillChecklistId: string,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
  ) {
    const workspace = await this.manager.findOne(AuditWorkspace, { id: auditWorkspaceId });
    const auditors = workspace?.auditors;
    if (!auditors.includes(user.id)) {
      throw new BaseError({ message: 'workspace.AUDITOR_FORBIDDEN_RESOURCE' });
    }
    // check fill check list status already finished ?
    const checklistDb = await this.manager.findOne(FillAuditChecklist, { id: fillChecklistId });
    const checklistStatusDb = checklistDb?.status;
    // if (checklistStatusDb === FillAuditChecklistStatus.COMPLETED) {
    //   throw new BaseError({ message: 'workspace.CHECKLIST_ALREADY_FINISHED' });
    // }
    const checklistStatus = FillAuditChecklistStatus.COMPLETED;
    const dataNoti: INotificationEventModel[] = [];
    const dataSendMail: IEmailEventModel[] = [];
    return await this.connection.transaction(async (manager) => {
      // update checklist status
      await manager.update(
        FillAuditChecklist,
        { id: fillChecklistId },
        {
          status: checklistStatus,
          lastUpdatedById: user.id,
          updatedAt: new Date(),
          publishOn: new Date(),
        },
      );

      // send notify when change status record workspace
      const listReceiverNoti = await manager
        .getCustomRepository(UserRepository)
        .listByIds(auditors);
      const performer = await manager
        .getCustomRepository(UserRepository)
        .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);

      if (
        checklistStatusDb === FillAuditChecklistStatus.IN_PROGRESS &&
        checklistStatus === FillAuditChecklistStatus.COMPLETED
      ) {
        dataNoti.push({
          receivers: listReceiverNoti as IUser[],
          module: ModuleType.AUDIT_WORKSPACE,
          recordId: auditWorkspaceId,
          recordRef: workspace.refNo,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: FillAuditChecklistStatus.COMPLETED,
          previousStatus: FillAuditChecklistStatus.IN_PROGRESS,
          performer: performer,
          executedAt: new Date(),
        });
      }
      return { dataNoti, dataSendMail };
    });
  }

  async updatedCheckListDetail(
    fillChecklistId: string,
    params: UpdateFillAuditChecklistDto,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
    checklistStatus: string,
  ) {
    try {
      const questsDto = params.fillQuestions;
      const questsDb = await this.listFillQuestionsOfFillChecklist(fillChecklistId, user);
      const questionAttFromDb = await this.manager
        .getCustomRepository(FillAuditChecklistQuestionRepository)
        .findAttachmentByIds(questsDto.map((x) => x.id));

      const preparedFillQuests: FillAuditChecklistQuestion[] = [];
      const preparedRoF = [];
      const preparedDeletedRoF = [];

      // check fill check list status already finished ?
      const checklistDb = await this.manager.findOne(FillAuditChecklist, { id: fillChecklistId });
      const checklistStatusDb = checklistDb?.status;

      // check role
      const workspace = await this.manager.findOne(AuditWorkspace, { id: auditWorkspaceId });
      const auditors = workspace?.auditors;
      if (!auditors.includes(user.id)) {
        throw new BaseError({ message: 'workspace.AUDITOR_FORBIDDEN_RESOURCE' });
      }

      const foundIarByPR = await this.connection
        .getCustomRepository(InternalAuditReportRepository)
        .findOne({
          where: {
            planningRequestId: workspace.planningRequestId,
          },
          select: ['id'],
        });

      //  prepared data
      const findingItemBody = [];
      for (const questDto of questsDto) {
        const questDb = questsDb.find((questDb) => questDb.id === questDto.id);

        if (!questDb) {
          throw new BaseError({ message: 'workspace.FILL_QUEST_ID_INVALID' });
        }

        //   next if question is not change
        if (this._checkChangeFillQuestion(questDto, questDb) === false) {
          continue;
        }

        // check can create/update ROF on remark-type-quest is specific
        let canCreatedRoF = true;
        if (questDb.chkQuestion.hasRemark === RemarkType.SPECIFIC) {
          // get list specific answer
          const intersectedAnswers = MySet.intersect(
            new Set(questDto.answers),
            new Set(questDb.chkQuestion.remarkSpecificAnswers),
          );
          // check if specific answer was picked or not
          if (intersectedAnswers.size === 0) {
            canCreatedRoF = false;
            if (questDto.reportFindingItem && questDto.reportFindingItem.id) {
              preparedDeletedRoF.push(questDto.reportFindingItem.id);
            }
          }
          if (
            intersectedAnswers.size != 0 &&
            (!questDto.reportFindingItem?.auditTypeId ||
              !questDto.reportFindingItem?.natureFindingId)
          ) {
            canCreatedRoF = false;
          }
        } else if (!questDb.chkQuestion.hasRemark) {
          canCreatedRoF = false;
        }

        const findingReportDto = questDto.reportFindingItem;

        if (findingReportDto && canCreatedRoF === true) {
          findingItemBody.push(findingReportDto);
          const reportFindingItemId = questDto.reportFindingItem.id || Utils.strings.generateUUID();
          preparedRoF.push({
            id: reportFindingItemId,
            auditTypeId: findingReportDto.auditTypeId,
            natureFindingId: findingReportDto.natureFindingId,
            isUpdatedFinding: findingReportDto?.isUpdatedFinding || false,
            isSignificant: findingReportDto.isSignificant,
            rectifiedOnBoard: findingReportDto.rectifiedOnBoard,
            mainCategoryId: findingReportDto.mainCategoryId,
            secondCategoryId: findingReportDto.secondCategoryId,
            thirdCategoryId: findingReportDto.thirdCategoryId,
            internalAuditReportId: foundIarByPR ? foundIarByPR.id : null,
            auditChecklistId: questDb.chkQuestion.auditChecklistId,
            chkQuestionId: questDb.chkQuestion.id,
            reference: findingReportDto.reference,
            auditWorkspaceId,
            createdUserId: user.id,
            findingComment: findingReportDto.findingComment,
            findingRemark: findingReportDto.findingRemark,
            locationId: questDb.locationId,
            companyId: user.companyId,
            findingAttachments: [...questDto.attachments, ...questDto.evidencePictures],
          });
          preparedFillQuests.push({
            id: questDb.id,
            findingRemark: questDto.findingRemark,
            answers: questDto.answers,
            evidencePictures: questDto.evidencePictures,
            attachments: questDto.attachments,
            memo: questDto.memo,
            reportFindingItemId,
            updatedUserId: user.id,
          } as FillAuditChecklistQuestion);
        } else {
          preparedFillQuests.push({
            id: questDb.id,
            answers: questDto.answers,
            findingRemark: questDto.findingRemark,
            evidencePictures: questDto.evidencePictures,
            attachments: questDto.attachments,
            memo: questDto.memo,
            updatedUserId: user.id,
          } as FillAuditChecklistQuestion);
        }
      }
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      //  save data
      return await this.connection.transaction(async (manager) => {
        let findingIds = questsDb.map((question) => question.reportFindingItemId);

        const currentItems = await manager.find(ReportFindingItem, {
          where: { id: In(findingIds) },
        });
        // save or update ROF data
        const updatedFindings = await manager.save(ReportFindingItem, preparedRoF);

        if (preparedDeletedRoF.length !== 0) {
          await manager
            .createQueryBuilder()
            .delete()
            .from(ReportFindingItem)
            .where('report_finding_item.id IN (:...ids)', { ids: preparedDeletedRoF })
            .execute();
        }

        // save fill quest
        // const updatedFillChkListQuestions = await manager.save(
        //   FillAuditChecklistQuestion,
        //   preparedFillQuests,
        // );
        const updatedFillChkListQuestions = [];
        for (const preparedFillQuest of preparedFillQuests) {
          updatedFillChkListQuestions.push(
            await manager.update(
              FillAuditChecklistQuestion,
              { id: preparedFillQuest.id },
              omit(preparedFillQuest, 'id'),
            ),
          );
        }

        // if fillChecklist is completed, release publishOn date, else return null
        const publishOn = checklistStatus == FillAuditChecklistStatus.COMPLETED ? new Date() : null;
        const submitOn = checklistDb.submitOn ? checklistDb.submitOn : new Date();
        const submitById = checklistDb.submitById ? checklistDb.submitById : user.id;
        // update checklist status
        await manager.update(
          FillAuditChecklist,
          { id: fillChecklistId },
          {
            status: checklistStatus,
            lastUpdatedById: user.id,
            updatedAt: new Date(),
            publishOn,
            submitOn,
            submitById,
          },
        );
        // send notify when change status record workspace
        const listReceiverNoti = await manager
          .getCustomRepository(UserRepository)
          .listByIds(auditors);
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);

        // Send noti data
        if (
          checklistStatusDb === FillAuditChecklistStatus.IN_PROGRESS &&
          checklistStatus === FillAuditChecklistStatus.COMPLETED
        ) {
          dataNoti.push({
            receivers: listReceiverNoti as IUser[],
            module: ModuleType.AUDIT_WORKSPACE,
            recordId: auditWorkspaceId,
            recordRef: workspace.refNo,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            currentStatus: FillAuditChecklistStatus.COMPLETED,
            previousStatus: FillAuditChecklistStatus.IN_PROGRESS,
            performer: performer,
            executedAt: new Date(),
          });
        }
        // trigger workspace status to DRAFT if exist checklist status equal COMPLETED
        if (
          checklistStatus !== FillAuditChecklistStatus.COMPLETED ||
          workspace.status === AuditWorkspaceStatus.DRAFT
        ) {
          await manager.update(
            AuditWorkspace,
            { id: auditWorkspaceId },
            {
              updatedUserId: user.id,
            },
          );
        } else {
          await manager.update(
            AuditWorkspace,
            { id: auditWorkspaceId },
            {
              status: AuditWorkspaceStatus.DRAFT,
              updatedUserId: user.id,
            },
          );
          // send notify when change status record workspace
          if (workspace.status === AuditWorkspaceStatus.NEW) {
            const listReceiverNoti: IUser[] = await manager
              .getCustomRepository(UserRepository)
              .listByIds(auditors);
            const performer = await manager
              .getCustomRepository(UserRepository)
              .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
            dataNoti.push({
              receivers: listReceiverNoti,
              module: ModuleType.AUDIT_WORKSPACE,
              recordId: auditWorkspaceId,
              recordRef: workspace.refNo,
              type: PushTypeEnum.CHANGE_RECORD_STATUS,
              currentStatus: 'In progress',
              previousStatus: AuditWorkspaceStatus.DRAFT,
              performer: performer,
              executedAt: new Date(),
            });
            for (const receiver of listReceiverNoti) {
              dataSendMail.push({
                receiver: receiver as IUserEmail,
                type: EmailTypeEnum.CHANGE_RECORD_STATUS,
                templateKey: MailTemplate.CHANGE_RECORD_STATUS,
                subject: '[Notification] Change status in a record',
                data: {
                  username: receiver.username,
                  baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                  recordRef: workspace.refNo,
                  recordId: auditWorkspaceId,
                  path: ModulePathEnum.AUDIT_INSPECTION_WORKSPACE,
                  previousStatus: AuditWorkspaceStatus.DRAFT,
                  currentStatus: 'In progress',
                  performer: performer,
                  executedAt: new Date(),
                },
              });
            }
          }
        }

        // trigger planing request status to In-Progress if checklistStatus is In-Progress
        if (checklistStatus === FillAuditChecklistStatus.IN_PROGRESS) {
          // Get user info
          const createdUser = await manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(user.id);

          await manager
            .getCustomRepository(PlanningRequestRepository)
            ._triggerInProgressPlanningRequest(workspace.planningRequestId, user.id, createdUser);

          // Handle add Audit log
          await manager.getCustomRepository(AuditLogRepository).createAuditLog(
            {
              module: AuditModuleEnum.INSPECTION_WORKSPACE,
              planningId: workspace.planningRequestId,
            },
            user,
            [AuditActivityEnum.IN_PROGRESS],
            createdUser,
          );
        }

        //Trigger generate ROF
        const rof = await manager.getCustomRepository(ReportFindingFormRepository).findOne({
          planningRequestId: workspace.planningRequestId,
        });

        const rofItems = await manager
          .getCustomRepository(ReportFindingItemRepository)
          .listFindingItemsByPR(workspace.planningRequestId, user);

        // Decrypt attachments from rofItems
        await Promise.all(
          rofItems.map(async (item) => {
            if (item?.findingAttachments && item?.findingAttachments.length > 0) {
              const decryptedAttachments = decryptAttachmentValues(item.findingAttachments);
              Object.assign(item, { findingAttachments: decryptedAttachments });
            }
            return item;
          }),
        );

        if (!rof) {
          const createdUser = await manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(user.id);

          const rofForm: CreateReportFindingFormDto = {
            planningRequestId: workspace.planningRequestId,
            timezone: params.timezone,
            reportFindingItems: rofItems,
            previousNCFindings: [],
            officeComment: '',
            comments: [],
            workflowRemark: '',
            isSubmit: false,
          };

          const rof = await manager
            .getCustomRepository(ReportFindingFormRepository)
            ._createReportFindingFormHelper(
              manager,
              rofForm,
              user,
              createdUser,
              true,
              fillChecklistId,
            );

          await manager.update(
            ReportFindingForm,
            { id: rof.id },
            {
              totalFindings: rofItems.length,
              totalNonConformity: rofItems.filter(
                (item) =>
                  item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
              ).length,
              totalObservation: rofItems.filter(
                (item) => item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.OBSERVATION,
              ).length,
            },
          );
        } else {
          const prepare = await manager
            .getCustomRepository(ReportFindingFormRepository)
            ._prepareFindingItems(user, rof.id, workspace.planningRequestId, rofItems, false);

          for (const item of prepare.preparedFindingItems) {
            delete item['findingAttachments'];
          }
          await manager.save(ReportFindingItem, prepare.preparedFindingItems);

          for (const preparedFindingItems of prepare.preparedFindingItemsForFillChecklist) {
            delete preparedFindingItems['findingAttachments'];
            await manager.update(
              FillAuditChecklistQuestion,
              {
                fillAuditChecklistId: fillChecklistId,
                chkQuestionId: preparedFindingItems.chkQuestionId,
              },
              preparedFindingItems,
            );
          }

          await manager.update(
            ReportFindingForm,
            { id: rof.id },
            {
              totalFindings: rofItems.length,
              totalNonConformity: rofItems.filter(
                (item) =>
                  item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
              ).length,
              totalObservation: rofItems.filter(
                (item) => item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.OBSERVATION,
              ).length,
            },
          );

          //duplicate CAR only once
          if (workspace?.isGenerateROF) {
            const dupCAR = await manager
              .getCustomRepository(ReportFindingFormRepository)
              ._triggerCreateCar(manager, user, rof.id, true);
            if (dupCAR.length > 0) {
              await manager.update(ReportFindingForm, { id: rof.id }, { isDuplicatedCAR: true });
              dupCAR.forEach(async (item) => {
                const carId = item.id;
                await manager.update(
                  FillAuditChecklistQuestion,
                  { fillAuditChecklistId: fillChecklistId, chkQuestionId: item.chkQuestionId },
                  { carId: carId },
                );
              });
            }
          }
        }

        // Generate onboard finding items
        const listFindingItemByWorkspace: ReportFindingItem[] = await manager
          .getCustomRepository(AuditWorkspaceRepository)
          .listFindingSummaries(auditWorkspaceId);

        const isGenerateROF = await manager
          .getCustomRepository(AuditWorkspaceRepository)
          ._isGenerateROF(auditWorkspaceId);
        if (listFindingItemByWorkspace.length > 0 && isGenerateROF) {
          const preparedOnboardFinding = [];
          for (const finding of listFindingItemByWorkspace) {
            const reportFindingItemId = finding.id;
            const appInstanceId = finding?.fillQuestion?.fillAuditChecklist?.appInstance;
            const webInstanceId = finding?.fillQuestion?.fillAuditChecklist?.webInstance;
            const templateCode = finding.auditChecklist.code;
            const questionName = finding.chkQuestion.question;
            const questionCode = finding.chkQuestion.code;

            preparedOnboardFinding.push({
              ...finding,
              reportFindingItemId,
              appInstanceId,
              webInstanceId,
              templateCode,
              questionName,
              questionCode,
              companyId: user.companyId,
            } as OnboardFindingItem);
          }

          await manager.save(OnboardFindingItem, preparedOnboardFinding);
        }

        // Handle finding item value history
        const checkIsCreate = findingItemBody.some(
          (findingBody) => !findingBody.hasOwnProperty('id'),
        );

        let listFindingItem = await this.manager.find(ReportFindingItem, {
          where: {
            id: In(findingIds),
          },
        });

        if (checkIsCreate) {
          findingIds = updatedFillChkListQuestions.map((question) => question.reportFindingItemId);
          listFindingItem = updatedFindings;
        }

        const userHistory = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(user.id);

        // save field change history: CREATE case
        const preparedValueChanges = [];
        if (checkIsCreate && listFindingItem.length > 0) {
          for (const finding of listFindingItem) {
            const findingBody = pick(finding, [
              'natureFindingName',
              'auditTypeName',
              'mainCategoryName',
              'secondCategoryName',
              'thirdCategoryName',
              'findingComment',
              'reference',
            ]);

            const valueChanges = commonCheckValueChange(finding, findingBody, true);

            if (valueChanges) {
              for (let i = 0; i < valueChanges.length; i++) {
                preparedValueChanges.push({
                  module: ModuleEnum.FINDING_ITEM,
                  action: ActionValueChangeEnum.CREATE,
                  createdUser: userHistory,
                  companyId: user.companyId,
                  recordId: finding.id,
                  key: valueChanges[i].key,
                  fieldLabel: valueChanges[i].fieldLabel,
                  oldValue: null,
                  newValue: valueChanges[i].newValue,
                });
              }
            }
          }
        }

        // save field change history: UPDATE case
        if (!checkIsCreate) {
          for (const finding of listFindingItem) {
            for (const findingBody of findingItemBody) {
              if (finding.id === findingBody.id) {
                // Apply new value after update to body
                Object.assign(findingBody, {
                  natureFindingName: finding.natureFindingName,
                  auditTypeName: finding.auditTypeName,
                  mainCategoryName: finding.mainCategoryName,
                  secondCategoryName: finding.secondCategoryName,
                  thirdCategoryName: finding.thirdCategoryName,
                });
              }
            }
          }
          for (const finding of currentItems) {
            for (const findingBody of findingItemBody) {
              if (finding.id === findingBody.id) {
                const newFindingBody = pick(findingBody, [
                  'natureFindingName',
                  'auditTypeName',
                  'mainCategoryName',
                  'secondCategoryName',
                  'thirdCategoryName',
                  'findingComment',
                  'reference',
                ]);

                const valueChanges = commonCheckValueChange(finding, newFindingBody);

                if (valueChanges) {
                  for (let i = 0; i < valueChanges.length; i++) {
                    preparedValueChanges.push({
                      module: ModuleEnum.FINDING_ITEM,
                      action: ActionValueChangeEnum.UPDATE,
                      createdUser: userHistory,
                      companyId: user.companyId,
                      recordId: finding.id,
                      key: valueChanges[i].key,
                      fieldLabel: valueChanges[i].fieldLabel,
                      oldValue: valueChanges[i].oldValue,
                      newValue: valueChanges[i].newValue,
                    });
                  }
                }
              }
            }
          }
        }
        for (const findingId of questionAttFromDb.keys()) {
          const finding = await this.manager
            .getCustomRepository(ReportFindingItemRepository)
            .findOne(findingId);
          const fillQuestionDb = questionAttFromDb.get(finding.id);
          if (fillQuestionDb) {
            const fillQuestionDto = preparedFillQuests.find(
              (x) => x.reportFindingItemId === finding.id,
            );
            const currentAtt = decryptAttachmentValues(fillQuestionDb);
            const newAtt = fillQuestionDto
              ? [
                  ...(fillQuestionDto?.attachments || []),
                  ...(fillQuestionDto?.evidencePictures || []),
                ]
              : [];
            const attDelete = Array.from(MySet.difference(new Set(currentAtt), new Set(newAtt)));
            const attAdd = Array.from(MySet.difference(new Set(newAtt), new Set(currentAtt)));
            const currentFindingAtt = fillQuestionDb?.length
              ? decryptAttachmentValues(fillQuestionDb)
              : [];
            const findingAtt = fillQuestionDb.length
              ? currentFindingAtt.filter((item) => !attDelete.includes(item))
              : [];
            const findingAttSync = Array.from(new Set([...findingAtt, ...attAdd]));
            updatedFillChkListQuestions.push(
              await manager
                .getCustomRepository(FillAuditChecklistQuestionRepository)
                .updateReportFinding(finding.id, findingAttSync),
            );

            if (finding.carId) {
              const carFound = await manager.findOne(CorrectiveActionRequest, {
                where: { id: finding.carId },
              });
              const carAttachment = carFound?.attachments?.length
                ? decryptAttachmentValues(carFound?.attachments)
                : [];
              const syncCarAtt = carAttachment?.length
                ? carAttachment.filter((item) => !attDelete.includes(item))
                : newAtt;
              const attSync = Array.from(new Set([...syncCarAtt, ...attAdd]));
              await manager
                .getCustomRepository(CARRepository)
                .updateCarAttachment(finding.carId, attSync);
            }
          }
        }
        await manager.save(ValueChangeHistory, preparedValueChanges);

        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      throw ex;
    }
  }

  async updatedAnswers(
    fillChecklistId: string,
    params: FillChecklistQuickAnswersDto,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
  ) {
    try {
      const questsDto = params.fillQuestions;
      const questsDb = await this.listFillQuestionsOfFillChecklist(fillChecklistId, user);

      const dataUpdate = [];
      // check role
      const workspace = await this.manager.findOne(AuditWorkspace, { id: auditWorkspaceId });
      const auditors = workspace?.auditors;
      if (!auditors.includes(user.id)) {
        throw new BaseError({ message: 'workspace.AUDITOR_FORBIDDEN_RESOURCE' });
      }
      // check case
      for (let i = 0; i < questsDto.length; i++) {
        const questDto = questsDto[i];

        const questDb = questsDb.find((questDb) => questDb.id === questDto.id);

        // check fill-quest-id valid?
        if (!questDb) {
          throw new BaseError({ message: 'workspace.FILL_QUEST_ID_INVALID' });
        }
        dataUpdate.push({
          id: questDb.id,
          findingRemark: questDb.findingRemark,
          answers: questsDto[i].answers,
          evidencePictures: questDb.evidencePictures,
          attachments: questDb.attachments,
          memo: questDb.memo,
          reportFindingItemId: questDb.reportFindingItemId,
          updatedUserId: user.id,
        } as FillAuditChecklistQuestion);
      }
      //  save data
      return await this.connection.transaction(async (manager) => {
        // save fill quest
        await manager.save(FillAuditChecklistQuestion, dataUpdate);

        //Trigger generate ROF
        // const rof = await manager.getCustomRepository(ReportFindingFormRepository).findOne({
        //   planningRequestId: workspace.planningRequestId,
        // });

        // if (!rof) {
        //   const createdUser = await manager
        //     .getCustomRepository(UserRepository)
        //     ._getUserInfoForHistory(user.id);

        //   const rofItems = await manager
        //     .getCustomRepository(ReportFindingItemRepository)
        //     .listFindingItemsByPR(workspace.planningRequestId, user);

        //   const rofForm: CreateReportFindingFormDto = {
        //     planningRequestId: workspace.planningRequestId,
        //     timezone: params.timezone,
        //     reportFindingItems: rofItems,
        //     previousNCFindings: [],
        //     officeComment: '',
        //     comments: [],
        //     workflowRemark: '',
        //     isSubmit: false,
        //   };

        //   await manager
        //     .getCustomRepository(ReportFindingFormRepository)
        //     ._createReportFindingFormHelper(manager, rofForm, user, createdUser);
        // }

        return 1;
      });
    } catch (ex) {
      throw ex;
    }
  }

  async submitFinalChecklist(
    fillChecklistId: string,
    user: TokenPayloadModel,
    auditWorkspaceId: string,
  ) {
    // const fillQuestionParamArr = params.fillQuestions;
    // const fillQuestionDbArr = await this.listFillQuestionsOfFillChecklist(fillChecklistId, user);
    //
    // for (let i = 0; i < fillQuestionParamArr.length; i++) {
    //   const fillQuestionParam = fillQuestionParamArr[i];
    //
    //   const fillQuestionDb = fillQuestionDbArr.find(
    //     (questDb) => questDb.id === fillQuestionParam.id,
    //   );
    //
    //   // check fill-quest-id valid?
    //   if (!fillQuestionDb) {
    //     throw new BaseError({ message: 'workspace.FILL_QUEST_ID_INVALID' });
    //   }
    //
    //   // check if not answer
    //   if (fillQuestionDb.chkQuestion.isMandatory && fillQuestionParam.answers.length === 0) {
    //     throw new BaseError({ message: 'workspace.REQUIRED_ANSWERS' });
    //   }
    //
    //   // check if answer need evident picture
    //   if (
    //     fillQuestionDb.chkQuestion.requireEvidencePicture &&
    //     fillQuestionParam.evidencePictures.length === 0
    //   ) {
    //     throw new BaseError({ message: 'workspace.REQUIRED_PICTURES' });
    //   }
    //
    //   // check if answer need attachments
    //   if (
    //     fillQuestionDb.chkQuestion.minPictureRequired &&
    //     fillQuestionParam.attachments.length === 0
    //   ) {
    //     throw new BaseError({ message: 'workspace.REQUIRED_ATTACHMENTS' });
    //   }
    //
    //   // check if remarked quest need finding-commend
    //   if (
    //     fillQuestionDb.chkQuestion.hasRemark === RemarkType.ALL &&
    //     !fillQuestionParam.reportFindingItem.findingComment
    //   ) {
    //     throw new BaseError({ message: 'workspace.REQUIRED_REMARKED_ANSWER' });
    //   }
    //   if (
    //     fillQuestionDb.chkQuestion.hasRemark === RemarkType.SPECIFIC &&
    //     fillQuestionDb.chkQuestion.remarkSpecificAnswers.length !== 0
    //   ) {
    //     // check if specific remarked-answer does not have finding-commend
    //     const listIntersectOfAnswer = MySet.intersect(
    //       new Set(fillQuestionDb.chkQuestion.remarkSpecificAnswers),
    //       new Set(fillQuestionParam.answers),
    //     );
    //
    //     if (
    //       listIntersectOfAnswer.size !== 0 &&
    //       (!fillQuestionParam.reportFindingItem ||
    //         !fillQuestionParam.reportFindingItem.findingComment)
    //     ) {
    //       throw new BaseError({ message: 'workspace.REQUIRED_REMARKED_ANSWER' });
    //     }
    //   }
    // }

    try {
      return await this.completedCheckListDetail(fillChecklistId, user, auditWorkspaceId);
    } catch (ex) {
      throw ex;
    }
  }

  _checkChangeFillQuestion(
    questDto: FillChecklistQuestionDto,
    questDb: FillAuditChecklistQuestion,
  ) {
    let isUpdated =
      !_.isEqual(questDto.answers.sort(), questDb.answers.sort()) ||
      questDto.findingRemark !== questDb.findingRemark ||
      questDto.memo !== questDb.memo ||
      !_.isEqual(questDto.evidencePictures.sort(), questDb.evidencePictures.sort()) ||
      !_.isEqual(questDto.attachments.sort(), questDb.attachments.sort());

    // compare 2 RofDB and RoFDto
    if (questDto.reportFindingItem && questDb.reportFindingItem) {
      isUpdated =
        isUpdated ||
        questDto.reportFindingItem.auditTypeId !== questDb.reportFindingItem.auditTypeId ||
        questDto.reportFindingItem.natureFindingId !== questDb.reportFindingItem.natureFindingId ||
        questDto.reportFindingItem.isSignificant !== questDb.reportFindingItem.isSignificant ||
        questDto.reportFindingItem.rectifiedOnBoard !==
          questDb.reportFindingItem.rectifiedOnBoard ||
        questDto.reportFindingItem.mainCategoryId !== questDb.reportFindingItem.mainCategoryId ||
        questDto.reportFindingItem.secondCategoryId !==
          questDb.reportFindingItem.secondCategoryId ||
        questDto.reportFindingItem.thirdCategoryId !== questDb.reportFindingItem.thirdCategoryId ||
        questDto.reportFindingItem.findingComment !== questDb.reportFindingItem.findingComment;
    }
    return isUpdated;
  }

  async getFillCkListDataForPdfRender(
    workSpaceId: string,
    fillCkListId: string,
    user: TokenPayloadModel,
  ) {
    try {
      const fillChecklistDetail = await this.connection
        .getCustomRepository(FillAuditChecklistRepository)
        .createQueryBuilder('fillAuditChecklist')
        .leftJoin('fillAuditChecklist.auditChecklist', 'auditChecklist')
        .select(['fillAuditChecklist.id'])
        .addSelect(['auditChecklist.id', 'auditChecklist.auditEntity'])
        .where('fillAuditChecklist.id =:fillCheckListId AND fillAuditChecklist.deleted = false', {
          fillCheckListId: fillCkListId,
          companyId: user.companyId,
        })
        .getOne();
      if (fillChecklistDetail.auditChecklist.auditEntity === AuditEntity.OFFICE) {
        const rawQuery = `SELECT
                            string_agg(cqa."content", ',') AS "answers",
                            fac."webInstance",
                            fac."appInstance",
                            fac.status,
                            ac."publishedDate" AS "publishOn" ,
                            ac."revisionDate" AS "revisionDate" ,
                            ac."revisionNumber",
                            ac."name" AS "ChecklistName",
                            cq."order" ,
                            u.username ,
                            facq."updatedAt" AS "updatedAt" ,
                            rfi."findingComment",
                            cq."question",
                            cq."hint",
                            facq."findingRemark" AS "findingRemark"
                          FROM
                            fill_audit_checklist_question facq
                            LEFT JOIN chk_question_answer cqa 
                          ON
                            cqa.id = ANY (facq.answers)
                          JOIN fill_audit_checklist fac 
                          ON
                            facq."fillAuditChecklistId" = fac.id
                          JOIN audit_checklist ac 
                          ON
                            ac.id = fac."auditChecklistId"
                          JOIN chk_question cq 
                          ON
                            cq.id = facq."chkQuestionId"
                          LEFT JOIN report_finding_item rfi 
                          ON
                            rfi.id = facq."reportFindingItemId"
                          LEFT JOIN "user" u 
                            ON
                            u.id = facq."updatedUserId"
                          WHERE
                            facq."fillAuditChecklistId" = $1
                          GROUP BY
                            facq.id,
                            fac."webInstance",
                            fac."appInstance",
                            fac.status ,
                            fac."publishOn",
                            ac."revisionNumber" ,
                            ac."revisionDate",
                            ac."name" ,
                            cq."order" ,
                            u.username,
                            rfi."findingComment",
                            cq."question",
                            cq."hint",
                            ac."publishedDate" 
                          ORDER BY
                            cq."order"`;

        return await this.manager.query(rawQuery, [fillCkListId]);
      } else {
        const auditWorkSpace = await this.connection
          .getCustomRepository(AuditWorkspaceRepository)
          .createQueryBuilder('auditWorkspace')
          .leftJoin('auditWorkspace.vessel', 'vessel')
          .leftJoin('vessel.vesselType', 'vesselType')
          .select(['auditWorkspace.id', 'vessel.id', 'vesselType.id'])
          .where(
            'auditWorkspace.id = :id AND auditWorkspace.companyId = :companyId AND auditWorkspace.deleted = false',
            {
              id: workSpaceId,
              companyId: user.companyId,
            },
          )
          .getOne();
        if (!auditWorkSpace) {
          throw new BaseError({ message: 'auditWorkspace.NOT_FOUND' });
        }

        const rawQuery = `SELECT
                            string_agg(cqa."content", ',') AS "answers",
                            fac."webInstance",
                            fac."appInstance",
                            fac.status,
                            ac."publishedDate" AS "publishOn" ,
                            ac."revisionDate" AS "revisionDate" ,
                            ac."revisionNumber",
                            ac."name" AS "ChecklistName",
                            cq."order" ,
                            u.username ,
                            facq."updatedAt" AS "updatedAt" ,
                            rfi."findingComment",
                            cq."question",
                            cq."hint",
                            facq."findingRemark" AS "findingRemark"
                          FROM
                            fill_audit_checklist_question facq
                            LEFT JOIN chk_question_answer cqa 
                          ON
                            cqa.id = ANY (facq.answers)
                          JOIN fill_audit_checklist fac 
                          ON
                            facq."fillAuditChecklistId" = fac.id
                          JOIN audit_checklist ac 
                          ON
                            ac.id = fac."auditChecklistId"
                          JOIN chk_question cq 
                          ON
                            cq.id = facq."chkQuestionId"
                          LEFT JOIN report_finding_item rfi 
                          ON
                            rfi.id = facq."reportFindingItemId"
                          LEFT JOIN "user" u 
                            ON
                            u.id = facq."updatedUserId"
                          LEFT JOIN chk_question_master_table cqmt 
                          ON cqmt."chkQuestionId" = cq.id
                          WHERE
                            facq."fillAuditChecklistId" = $1
                          AND (ac."auditEntity" = $2 OR (ac."auditEntity" = $3 AND cqmt."valueId" = $4 AND cqmt."masterTableId" = $5))
                          GROUP BY
                            facq.id,
                            fac."webInstance",
                            fac."appInstance",
                            fac.status ,
                            fac."publishOn",
                            ac."revisionNumber" ,
                            ac."revisionDate",
                            ac."name" ,
                            cq."order" ,
                            u.username,
                            rfi."findingComment",
                            cq."question",
                            cq."hint",
                            ac."publishedDate" 
                          ORDER BY
                            cq."order"`;

        return await this.manager.query(rawQuery, [
          fillCkListId,
          AuditEntity.OFFICE,
          AuditEntity.VESSEL,
          auditWorkSpace?.vessel?.vesselType?.id || null,
          MasterTableObj.VESSEL_TYPE,
        ]);
      }
    } catch (ex) {
      throw ex;
    }
  }

  async getFillCkListDataForUpdatedROFRender(workSpaceId: string, user: TokenPayloadModel) {
    // Genneral Information
    const workspace = await this.connection
      .getCustomRepository(AuditWorkspaceRepository)
      .createQueryBuilder('workSpace')
      .leftJoin('workSpace.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .leftJoin('auditCompany.country', 'countryCompany')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vesselDocHolders.company', 'vesselDocHolderCompany')
      .leftJoin('auditCompany.companyTypes', 'companyTypes')
      .leftJoin('vessel.country', 'countryVessel')
      .where('workSpace.id = :workSpaceId AND workSpace.companyId = :companyId', {
        workSpaceId: workSpaceId,
        companyId: user.companyId,
      })
      .select(['workSpace.id', 'workSpace.isGenerateROF'])
      .addSelect([
        'planningRequest.id',
        'planningRequest.auditNo',
        'planningRequest.entityType',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.dateOfLastInspection',
        'planningRequest.fromPortId',
        'planningRequest.isSA',
        'auditors.username',
        'fromPort.name',
        'vessel.id',
        'vessel.name',
        'vessel.imoNumber',
        'vessel.countryId',
        'countryVessel.name',
        'vessel.countryId',
        'vessel.country',
        'vesselType.name',
        'leadAuditor.username',
        'auditCompany.name',
        'auditCompany.companyIMO',
        'auditCompany.countryId',
        'countryCompany.name',
        'auditCompany.countryId',
        'companyTypes.companyType',
        'vesselDocHolders.id',
        'vesselDocHolderCompany.name',
        'auditTypes.name',
      ])
      .getOne();

    // getting inspection company name for vesselInspection pdf generation
    const inspectionCompany = await this.connection
      .getCustomRepository(UserRepository)
      .createQueryBuilder('user')
      .leftJoin('user.company', 'company')
      .addSelect(['user.id', 'company.name'])
      .where('user.username = :username', {
        username: workspace?.planningRequest?.leadAuditor?.username,
      })
      .getOne();

    const generalInfor = {
      auditNo: workspace.planningRequest?.auditNo,
      isVessel: workspace.planningRequest?.entityType === AuditEntity.VESSEL ? true : false,
      dateOfInspection: workspace.planningRequest?.plannedFromDate,
      inspectors: arrayToStringCustom(workspace.planningRequest?.auditors, 'username'),
      leadInspector: workspace.planningRequest?.leadAuditor?.username,
      reportDate: new Date(Date.now()),
      nameInspectors: workspace.planningRequest?.auditors?.map((auditor) => auditor.username),
      isGenerateROF: workspace.isGenerateROF,
      inspectionTypes: arrayToStringCustom(workspace.planningRequest.auditTypes, 'name'),
      vesselName: '',
      companyName: '',
      InspectionCompanyName: inspectionCompany?.company?.name,
    };
    Object.assign(generalInfor, {isSA: workspace.planningRequest?.isSA});
    if (workspace.planningRequest.entityType === AuditEntity.VESSEL) {
      const vesselData = workspace.planningRequest.vessel;
      Object.assign(generalInfor, {
        vesselName: vesselData.name,
        vesselType: vesselData.vesselType?.name,
        vesselIMO: vesselData.imoNumber,
        shipManagementCompany: vesselData?.vesselDocHolders[0]?.company?.name || '',
        // flag: vesselData.countryFlag,
        flag: vesselData?.country?.name || '',
        portOfInspection: workspace.planningRequest?.fromPort?.name || '',
      });
    } else {
      const auditCompanyData = workspace.planningRequest.auditCompany;
      Object.assign(generalInfor, {
        companyName: auditCompanyData?.name,
        companyIMO: auditCompanyData.companyIMO,
        companyTypes: arrayToStringCustom(auditCompanyData.companyTypes, 'companyType'),
        country: auditCompanyData?.country?.name || '',
      });
    }
    let mappedData = [];
    if (!workspace?.planningRequest?.isSA) {
      const findingItems = await this.connection
        .getCustomRepository(ReportFindingItemRepository)
        .createQueryBuilder('reportFindingItem')
        .leftJoin('reportFindingItem.auditWorkspace', 'auditWorkspace')
        .leftJoin('reportFindingItem.chkQuestion', 'chkQuestion')
        .select([
          'reportFindingItem.id',
          'reportFindingItem.auditTypeName',
          'reportFindingItem.mainCategoryName',
          'reportFindingItem.findingComment',
          'reportFindingItem.rectifiedOnBoard',
          'reportFindingItem.natureFindingName',
        ])
        .addSelect(['chkQuestion.question', 'chkQuestion.code'])
        .where(
          'reportFindingItem.auditWorkspaceId = :id AND auditWorkspace.companyId = :companyId',

          {
            id: workSpaceId,
            companyId: user.companyId,
          },
        )
        .orderBy('chkQuestion.order', 'ASC')
        .getMany();
      const transformedData = {};
      if (findingItems?.length) {
        findingItems?.map((item) => {
          if (!transformedData[item?.auditTypeName]) {
            transformedData[item?.auditTypeName] = {};
          }
          if (!transformedData[item?.auditTypeName][item?.natureFindingName]) {
            transformedData[item?.auditTypeName][item?.natureFindingName] = [];
          }
          transformedData[item?.auditTypeName][item?.natureFindingName].push({
            findingId: item?.id,
            chkQuestion: item?.chkQuestion,
            questionCode: item?.chkQuestion?.code || '',
            question: item?.chkQuestion?.question || '',
            mainCategory: item?.mainCategoryName || '',
            findingRemark: item?.findingComment || '',
            recitification: item?.rectifiedOnBoard ? 'Yes' : '',
            auditType: item?.auditTypeName,
          });
        });
        mappedData = Object.keys(transformedData)?.map((auditTypeKey) => ({
          auditType: auditTypeKey,
          findingItems: Object.keys(transformedData[auditTypeKey]).map((natureFindingKey) => ({
            natureFindingName: natureFindingKey,
            items: transformedData[auditTypeKey][natureFindingKey],
          })),
        }));
      }
    } else {
      const findingItems = await this.connection
        .getRepository(SAFindingItem)
        .createQueryBuilder('saFindingItems')
        .innerJoin('saFindingItems.auditWorkspace', 'auditWorkspace')
        .leftJoin('saFindingItems.elementMaster', 'elementMaster')
        .select([
          'saFindingItems.id',
          'saFindingItems.findingComment',
          'saFindingItems.findingRemark',
          'saFindingItems.natureFindingName',
          'saFindingItems.auditTypeName',
          'elementMaster.name',
          'elementMaster.code',
          'elementMaster.keyPerformanceIndicator',
          'elementMaster.elementStageQ',
        ])
        .where(
          'saFindingItems.auditWorkspaceId = :workSpaceId AND auditWorkspace.companyId = :companyId',
          { workSpaceId, companyId: user.companyId },
        )
        .getMany();
      const transformedData = {};
      if (findingItems?.length) {
        findingItems?.map((item) => {
          if (!transformedData[item?.auditTypeName]) {
            transformedData[item?.auditTypeName] = {};
          }
          if (!transformedData[item?.auditTypeName][item?.natureFindingName]) {
            transformedData[item?.auditTypeName][item?.natureFindingName] = [];
          }
          transformedData[item?.auditTypeName][item?.natureFindingName].push({
            findingId: item?.id,
            chkQuestion: item?.elementMaster,
            questionCode: item?.elementMaster?.elementStageQ || '',
            question: item?.elementMaster?.keyPerformanceIndicator || '',
            mainCategory: item?.elementMaster?.name || '',
            findingRemark: item?.findingRemark || '',
            recitification: '',
            auditType: item?.auditTypeName,
          });
        });
        mappedData = Object.keys(transformedData)?.map((auditTypeKey) => ({
          auditType: auditTypeKey,
          findingItems: Object.keys(transformedData[auditTypeKey]).map((natureFindingKey) => ({
            natureFindingName: natureFindingKey,
            items: transformedData[auditTypeKey][natureFindingKey],
          })),
        }));
      }
    }
    return {
      workspace,
      generalInfor,
      data: mappedData,
    };
  }

  async checkFillChecklistAndFindings(user: TokenPayloadModel, auditWorkspaceId: string) {
    // Get planning request details
    const planningRequest = await this.getAuditWorkspacePlanningRequest(auditWorkspaceId);

    // Retrieve necessary data asynchronously using Promise.all
    const [fillChecklist, reportFindingForm, iar] = await Promise.all([
      this.manager.find(FillAuditChecklist, { auditWorkspaceId }),
      this.getReportFindingFormByPlanningRequestId(planningRequest.planningRequestId),
      this.getInternalAuditReportByPlanningRequestId(planningRequest.planningRequestId),
    ]);

    // Retrieve fill checklist questions and answers for single and multiple checklist

    const findingChecklists = [];
    await Promise.all(
      fillChecklist?.map(async (item) => {
        const listFillQuestions = await this.listFillQuestionsOfFillChecklist(item?.id, user);
        const filteredChecklists = listFillQuestions?.filter((questDB) => {
          const hasRemark = questDB?.chkQuestion?.hasRemark === 'All';
          const hasAnswers = questDB?.answers?.length > 0;
          const remarkSpecificAnswers = questDB?.chkQuestion?.remarkSpecificAnswers ?? [];

          const hasRemarkSpecificAnswer = remarkSpecificAnswers.some((item) =>
            questDB?.answers?.includes(item),
          );

          return (hasRemark && hasAnswers) || hasRemarkSpecificAnswer;
        });

        if (filteredChecklists) {
          findingChecklists.push(...filteredChecklists);
        }
        // findingChecklists.push(
        //   ...listFillQuestions?.filter(
        //     (questDB) =>
        //       (questDB?.chkQuestion?.hasRemark === 'All' && questDB?.answers?.length > 0) ||
        //       questDB?.chkQuestion?.remarkSpecificAnswers.find((item) =>
        //         questDB?.answers?.includes(item),
        //       ),
        //   ),
        // );
      }),
    );

    // Retrieve existing findings
    const findings = await this.getReportFindingItems(auditWorkspaceId, reportFindingForm, iar);

    // Filter checklist questions with differences
    // const findingChecklists = questsDb.filter(
    //   (questDB) =>
    //     (questDB.chkQuestion.hasRemark === 'All' && questDB.answers.length > 0) ||
    //     questDB.chkQuestion.remarkSpecificAnswers.find((item) => questDB.answers.includes(item)),
    // );

    // Find differences in questions and item IDs
    const [diffQuestions, diffItemIds] = this.findDifferences(findingChecklists, findings);

    let noOfFindingUpdated: any[];
    // Check if there are differences
    if (diffQuestions.size > 0 || diffItemIds.size > 0) {
      // Delete existing report finding items
      await this.deleteReportFindingItems(auditWorkspaceId, reportFindingForm, iar);

      // Update and insert new findings
      noOfFindingUpdated = await this.updateAndInsertFindings(
        auditWorkspaceId,
        fillChecklist,
        findingChecklists,
        reportFindingForm,
        iar,
        user,
      );
    }

    // Retrieve the report finding form for planning request
    const rof = await this.manager.find(ReportFindingForm, {
      planningRequestId: planningRequest.planningRequestId,
    });

    // Handle duplicate CARs
    if (rof[0]?.isDuplicatedCAR) {
      await this.handleDuplicateCARs(
        findingChecklists,
        planningRequest.planningRequestId,
        rof[0],
        user,
      );
    }

    // Check if any findings were updated
    if (noOfFindingUpdated?.length > 0) {
      // Update totals in report finding form
      await this.updateReportFindingFormTotals(reportFindingForm, findingChecklists);
      return { status: 200, message: `${noOfFindingUpdated.length} findings are updated.` };
    } else {
      return { status: 200, message: `No mismatch findings.` };
    }
  }

  // Helper function to retrieve planning request details
  private async getAuditWorkspacePlanningRequest(auditWorkspaceId: string) {
    return this.connection
      .getCustomRepository(AuditWorkspaceRepository)
      .createQueryBuilder('auditWorkspace')
      .where('auditWorkspace.id = :auditWorkspaceId', { auditWorkspaceId })
      .select(['auditWorkspace.planningRequestId'])
      .getOne();
  }

  // Helper function to retrieve report finding form by planning request ID
  private async getReportFindingFormByPlanningRequestId(planningRequestId: string) {
    return this.connection
      .getCustomRepository(ReportFindingFormRepository)
      .createQueryBuilder('reportFindingForm')
      .where('reportFindingForm.planningRequestId = :planningRequestId', { planningRequestId })
      .select()
      .getOne();
  }

  // Helper function to retrieve internal audit report by planning request ID
  private async getInternalAuditReportByPlanningRequestId(planningRequestId: string) {
    return this.connection
      .getCustomRepository(InternalAuditReportRepository)
      .createQueryBuilder('internalAuditReport')
      .where('internalAuditReport.planningRequestId = :planningRequestId', { planningRequestId })
      .select()
      .getOne();
  }

  // Helper function to retrieve report finding items
  private async getReportFindingItems(
    auditWorkspaceId: string,
    reportFindingForm: ReportFindingForm,
    iar: InternalAuditReport,
  ) {
    return this.connection
      .getCustomRepository(ReportFindingItemRepository)
      .createQueryBuilder('reportFindingItem')
      .where(
        '(reportFindingItem.auditWorkspaceId = :auditWorkspaceId OR reportFindingItem.reportFindingFormId = :reportFindingFormId OR reportFindingItem.internalAuditReportId = :internalAuditReportId) AND (reportFindingItem.auditChecklistId IS NOT NULL AND reportFindingItem.chkQuestionId IS NOT NULL)',
        {
          auditWorkspaceId,
          reportFindingFormId: reportFindingForm?.id,
          internalAuditReportId: iar?.id,
        },
      )
      .select()
      .getMany();
  }

  // Helper function to find differences in questions and item IDs
  private findDifferences(findingChecklists: any[], findings: any[]) {
    const diffQuestions = this.checkDifference(
      new Set(findingChecklists.map((item) => item.chkQuestionId)),
      new Set(findings.map((item) => item.chkQuestionId)),
    );
    console.log('diffQuestions: ', diffQuestions);

    const diffItemIds = this.checkDifference(
      new Set(findings.map((item) => item.id)),
      new Set(findingChecklists.map((item) => item.reportFindingItemId)),
    );
    console.log('diffItemIds: ', diffItemIds);

    return [diffQuestions, diffItemIds];
  }

  // Helper function to delete report finding items
  private async deleteReportFindingItems(
    auditWorkspaceId: string,
    reportFindingForm: ReportFindingForm,
    iar: InternalAuditReport,
  ) {
    const deleted = await this.manager
      .createQueryBuilder()
      .delete()
      .from(ReportFindingItem)
      .where(
        `("auditWorkspaceId" = :auditWorkspaceId OR "reportFindingFormId" = :reportFindingFormId OR "internalAuditReportId" = :internalAuditReportId) AND ("auditChecklistId" IS NOT NULL AND "chkQuestionId" IS NOT NULL)`,
        {
          auditWorkspaceId,
          reportFindingFormId: reportFindingForm?.id,
          internalAuditReportId: iar?.id,
        },
      )
      .execute();
    console.log('deleted: ', deleted);
  }

  // Helper function to update and insert findings
  private async updateAndInsertFindings(
    auditWorkspaceId: string,
    fillChecklist: any,
    findingChecklists: any[],
    reportFindingForm: ReportFindingForm,
    iar: InternalAuditReport,
    user: TokenPayloadModel,
  ) {
    const noOfFindingUpdated = [];
    fillChecklist?.map(async (fillchecklist) => {
      await Promise.all(
        findingChecklists?.map(async (findingItem) => {
          if (fillchecklist?.id === findingItem?.fillAuditChecklistId) {
            //Update finding Checklist if Car is deleted
            let carFound = null;
            if (findingItem?.carId) {
              carFound = await this.manager.findOne(CorrectiveActionRequest, {
                where: { id: findingItem.carId },
              });
              if (!carFound) {
                findingItem.carId = null;
              }
            }
            const id = Utils.strings.generateUUID();
            const preparedRoF = {
              id: id,
              reportFindingFormId: reportFindingForm?.id,
              internalAuditReportId: iar?.id,
              auditTypeId: findingItem?.auditTypeId,
              auditTypeName: findingItem?.auditTypeName,
              natureFindingId: findingItem?.natureFindingId,
              natureFindingName: findingItem?.natureFindingName,
              isSignificant: findingItem?.isSignificant,
              rectifiedOnBoard: findingItem?.rectifiedOnBoard,
              mainCategoryId: findingItem?.mainCategoryId,
              mainCategoryName: findingItem?.mainCategoryName,
              secondCategoryId: findingItem?.secondCategoryId,
              secondCategoryName: findingItem?.secondCategoryName,
              thirdCategoryId: findingItem?.thirdCategoryId,
              thirdCategoryName: findingItem?.thirdCategoryName,
              remark: findingItem?.remark,
              auditChecklistId: fillchecklist?.auditChecklistId,
              chkQuestionId: findingItem?.chkQuestionId,
              reference: findingItem?.reference,
              auditWorkspaceId,
              createdUserId: user.id,
              findingComment: findingItem?.findingComment,
              findingRemark: findingItem?.remark,
              locationId: findingItem?.locationId,
              locationName: findingItem?.locationName,
              carId: findingItem?.carId,
              companyId: user.companyId,
              observedRiskId: findingItem?.reportFindingItem?.observedRiskId,
              // viqId: viqId,
              // departmentId: departmentId,
            };

            const reInsert = await this.manager.save(ReportFindingItem, preparedRoF);

            if (reInsert) {
              await this.manager.update(
                FillAuditChecklistQuestion,
                {
                  fillAuditChecklistId: fillchecklist.id,
                  chkQuestionId: findingItem?.chkQuestion?.id,
                },
                { reportFindingItemId: reInsert?.id, carId: findingItem?.carId },
              );
              const questionUpdate = await this.manager
                .getCustomRepository(FillAuditChecklistRepository)
                .getAttByFindingItemIds([reInsert?.id], user);

              const attachmentSync = decryptAttachmentValues(questionUpdate.get(reInsert?.id));
              await this.updateReportFinding(reInsert?.id, attachmentSync);
              if (carFound) {
                const attSync = Array.from(
                  new Set([...decryptAttachmentValues(carFound?.attachments), ...attachmentSync]),
                );
                await this.manager
                  .getCustomRepository(CARRepository)
                  .updateCarAttachment(reInsert.carId, attSync);
              }

              noOfFindingUpdated.push(reInsert);
            }
          }
        }),
      );
    });
    return noOfFindingUpdated;
  }

  // Helper function to handle duplicate CARs
  private async handleDuplicateCARs(
    findingChecklists: any[],
    planningRequestId: string,
    rof: ReportFindingForm,
    user: TokenPayloadModel,
  ) {
    const car = await this.manager.find(CorrectiveActionRequest, { planningRequestId });
    const cars = car.map((item) => item.chkQuestionId);

    const checklistCars = findingChecklists
      .filter((item) => item.carId)
      .map((item) => item.chkQuestionId);

    const diffCar = MySet.difference(new Set(checklistCars), new Set(cars));
    console.log('diffCar: ', diffCar);

    if (diffCar.size > 0) {
      const dupCAR = await this.manager
        .getCustomRepository(ReportFindingFormRepository)
        ._triggerCreateCar(this.manager, user, rof.id, true);

      dupCAR.forEach(async (item) => {
        const carId = item.id;
        await this.manager.update(
          FillAuditChecklistQuestion,
          { reportFindingFormId: rof.id, chkQuestionId: item.chkQuestionId },
          { carId },
        );
      });

      if (dupCAR.length > 0) {
        await this.manager.update(ReportFindingForm, { id: rof.id }, { isDuplicatedCAR: true });
      }
    }
  }

  // Helper function to update totals in report finding form
  private async updateReportFindingFormTotals(
    reportFindingForm: ReportFindingForm,
    findingChecklists: any[],
  ) {
    await this.manager.update(
      ReportFindingForm,
      { id: reportFindingForm.id },
      {
        totalFindings: findingChecklists.length,
        totalNonConformity: findingChecklists.filter(
          (item) => item.natureFindingName === AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
        ).length,
        totalObservation: findingChecklists.filter(
          (item) => item.natureFindingName === AppConst.NATURE_FINDING_DEFAULT.OBSERVATION,
        ).length,
      },
    );
  }
  async updateReportFinding(id, attachments: string[]) {
    if (attachments.length) {
      const rawQuery = `
    UPDATE report_finding_item 
    SET "findingAttachments" = '{${attachments}}'
    WHERE id = '${id}'
    `;

      await this.manager.query(rawQuery);
    }
  }
  async _supportGetFillCheckList(
    fillCheckListId,
    workSpaceId,
    user: TokenPayloadModel,
    isCheckComplete?: boolean,
    isGroupQuestion?: boolean,
    forRefData?: boolean,
    query?,
  ) {
    const queryBuild = this.createQueryBuilder('fillAuditChecklistQuestion')
      .leftJoinAndSelect('fillAuditChecklistQuestion.chkQuestion', 'chkQuestion')
      .leftJoin('chkQuestion.answerOptions', 'answerOptions')
      .leftJoin('fillAuditChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
      .leftJoin('chkQuestion.topic', 'topic')
      .leftJoin('chkQuestion.auditChecklist', 'auditChecklist')
      .leftJoin('auditChecklist.inspectionMappings', 'inspectionMappings')
      .leftJoin(
        'inspectionMappings.natureFindings',
        'natureFindings',
        'natureFindings.isPrimaryFinding = true',
      )
      .leftJoin('chkQuestion.referencesCategoryData', 'referencesCategoryData')
      .leftJoin('fillAuditChecklistQuestion.updatedUser', 'updatedUser')
      .leftJoinAndSelect('fillAuditChecklistQuestion.reportFindingItem', 'reportFindingItem')
      .select()
      .addSelect([
        'answerOptions.id',
        'answerOptions.content',
        'answerOptions.order',
        'topic.name',
        'auditChecklist.id',
        'auditChecklist.auditEntity',
        'referencesCategoryData.id',
        'referencesCategoryData.masterTableId',
        'referencesCategoryData.valueId',
        'referencesCategoryData.value',
        'updatedUser.username',
        'inspectionMappings.id',
        'inspectionMappings.createdAt',
        'inspectionMappings.auditTypeId',
        'natureFindings.natureFindingId',
      ])
      .where('(fillAuditChecklistQuestion.fillAuditChecklistId =:fillCheckListId)', {
        fillCheckListId,
      })
      .addOrderBy('chkQuestion.order', 'ASC');
    // .addOrderBy('answerOptions.createdAt', 'ASC');

    if (isCheckComplete) {
      queryBuild.andWhere('fillAuditChecklist.auditTypeId = inspectionMappings.auditTypeId');
    }
    if (forRefData) {
      queryBuild
        .leftJoin('answerOptions.value', 'value')
        .addSelect(['value.id', 'value.number', 'value.status']);
    }
    if (isGroupQuestion) {
      queryBuild.andWhere(`fillAuditChecklistQuestion.answers = '{}'`);
    }
    if (!isCheckComplete && !forRefData && !isGroupQuestion) {
      queryBuild.addOrderBy('answerOptions.createdAt', 'ASC');
    } else {
      queryBuild.addOrderBy('answerOptions.order', 'ASC');
    }

    const fillChecklistDetail = await this.connection
      .getCustomRepository(FillAuditChecklistRepository)
      .createQueryBuilder('fillAuditChecklist')
      .leftJoin('fillAuditChecklist.auditChecklist', 'auditChecklist')
      .select(['fillAuditChecklist.id', 'fillAuditChecklist.inspectionMappingId'])
      .addSelect(['auditChecklist.id', 'auditChecklist.auditEntity'])
      .where('fillAuditChecklist.id =:fillCheckListId AND fillAuditChecklist.deleted = false', {
        fillCheckListId,
      })
      .getOne();

    if (workSpaceId && fillChecklistDetail.auditChecklist.auditEntity === AuditEntity.VESSEL) {
      const auditWorkSpace = await this.connection
        .getCustomRepository(AuditWorkspaceRepository)
        .createQueryBuilder('auditWorkspace')
        .leftJoin('auditWorkspace.vessel', 'vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        .select(['auditWorkspace.id', 'vessel.id', 'vesselType.id'])
        .where(
          'auditWorkspace.id = :id AND auditWorkspace.companyId = :companyId AND auditWorkspace.deleted = false',
          {
            id: workSpaceId,
            companyId: user.companyId,
          },
        )
        .getOne();
      if (!auditWorkSpace) {
        throw new BaseError({ message: 'auditWorkspace.NOT_FOUND' });
      }

      queryBuild.andWhere(
        `(auditChecklist.auditEntity = 'Office' OR (auditChecklist.auditEntity = 'Vessel' AND referencesCategoryData.valueId = :vesselTypeId AND referencesCategoryData.masterTableId = :masterTableId))`,
        {
          vesselTypeId: auditWorkSpace?.vessel?.vesselType?.id || null,
          masterTableId: MasterTableObj.VESSEL_TYPE,
        },
      );
    }
    if (forRefData || isGroupQuestion) {
      if (query.content) {
        queryBuild.andWhere(
          `(chkQuestion.code ILIKE :content OR chkQuestion.question ILIKE :content )`,
          { content: `%${query.content}%` },
        );
      }
      if (query.questionType === FilterListFillCheckListQuestionTypeEnum.MANDATORY) {
        queryBuild.andWhere(`(chkQuestion.isMandatory = true )`);
      } else if (query.questionType === FilterListFillCheckListQuestionTypeEnum.OPTIONAL) {
        queryBuild.andWhere(`(chkQuestion.isMandatory = false )`);
      }

      if (query.answered === FilterListFillCheckListAnsweredEnum.YES) {
        queryBuild.andWhere(`array_length(fillAuditChecklistQuestion.answers,1) > 0`);
      } else if (query.answered === FilterListFillCheckListAnsweredEnum.NO) {
        queryBuild.andWhere(`array_length(fillAuditChecklistQuestion.answers,1) is null`);
      }
      if (query.mandatoryFields && query.mandatoryFields.length > 0) {
        if (
          query.mandatoryFields.indexOf(
            FilterListFillCheckListMandatoryFieldsEnum.REMARK_FOR_FINDING,
          ) > -1 &&
          query.mandatoryFields.indexOf(
            FilterListFillCheckListMandatoryFieldsEnum.EVIDENCE_PICTURE,
          ) > -1
        ) {
          queryBuild.andWhere(
            `(chkQuestion.hasRemark is not null or chkQuestion.requireEvidencePicture = true)`,
          );
        } else {
          if (
            query.mandatoryFields.indexOf(
              FilterListFillCheckListMandatoryFieldsEnum.REMARK_FOR_FINDING,
            ) > -1
          ) {
            queryBuild.andWhere(`(chkQuestion.hasRemark is not null)`);
          } else if (
            query.mandatoryFields.indexOf(
              FilterListFillCheckListMandatoryFieldsEnum.EVIDENCE_PICTURE,
            ) > -1
          ) {
            queryBuild.andWhere(`(chkQuestion.requireEvidencePicture = true)`);
          }
        }
      }
    }
    const fillAuditChecklists = await queryBuild.getMany();
    return { fillAuditChecklists, fillChecklistDetail };
  }

  async syncDataToFindingItem(findingItemId: string, user: TokenPayloadModel) {
    const questionAttFromDb = await this.manager
      .getCustomRepository(FillAuditChecklistRepository)
      .getAttByFindingItemIds([findingItemId], user);
    const findingItem = await this.manager
      .getCustomRepository(ReportFindingItemRepository)
      .findOne(findingItemId);
    const attachmentFillQuestion = questionAttFromDb.get(findingItemId);

    const attachmentSync = decryptAttachmentValues(attachmentFillQuestion);

    const findingAtt = findingItem?.findingAttachments?.length
      ? findingItem.findingAttachments
      : [];
    findingAtt.push(...attachmentSync);
    await this.manager
      .getCustomRepository(FillAuditChecklistQuestionRepository)
      .updateReportFinding(findingItemId, findingAtt);
  }
  async findAttachmentByIds(ids: string[]) {
    const records = await this.findByIds(ids);
    const attachments = new Map<string, string[]>();

    records.forEach((att) => {
      const listAtt = [];
      if (att.attachments.length) {
        listAtt.push(...att.attachments);
      }
      if (att.evidencePictures.length) {
        listAtt.push(...att.evidencePictures);
      }
      attachments.set(att.reportFindingItemId, listAtt);
    });
    return attachments;
  }

  private checkDifference<T>(setA: Set<T>, setB: Set<T>) {
    // Combine elements from both sets into an array
    const combinedArray = [...setA, ...setB];

    // Filter the array to include only elements that are unique to either setA or setB
    const uniqueElementsArray = combinedArray.filter((elem) => !setA.has(elem) || !setB.has(elem));

    // Create a new set from the filtered array
    const differenceSet = new Set(uniqueElementsArray);

    return differenceSet;
  }

  async carGeneration(workspaceId: string, user: TokenPayloadModel) {
    const auditWorkspace = await getCustomRepository(AuditWorkspaceRepository).findOne({
      id: workspaceId,
    });

    const reportFindingForm = await getCustomRepository(ReportFindingFormRepository).findOne({
      planningRequestId: auditWorkspace?.planningRequestId,
    });
    const fillAuditChecklistQuestion = await getCustomRepository(
      FillAuditChecklistQuestionRepository,
    ).findOne({
      auditWorkspaceId: workspaceId,
    });
    return await this.connection.transaction(async (manager) => {
      if (!reportFindingForm?.isDuplicatedCAR) {
        const dupCAR = await getCustomRepository(ReportFindingFormRepository)._triggerCreateCar(
          manager,
          user,
          reportFindingForm.id,
          true,
        );
        if (dupCAR?.length > 0) {
          await manager.update(
            ReportFindingForm,
            { id: reportFindingForm?.id },
            { isDuplicatedCAR: true },
          );
          dupCAR.forEach(async (item) => {
            const carId = item.id;
            await manager.update(
              FillAuditChecklistQuestion,
              {
                fillAuditChecklistId: fillAuditChecklistQuestion?.fillAuditChecklistId,
                chkQuestionId: item?.chkQuestionId,
              },
              { carId: carId },
            );
          });
        }
      }
    });
  }
}
