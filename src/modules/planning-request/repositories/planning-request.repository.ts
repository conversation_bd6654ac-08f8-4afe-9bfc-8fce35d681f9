import { maxBy } from 'lodash';
import * as momentTZ from 'moment-timezone';
import {
  BaseError,
  CommonStatus,
  ForbiddenError,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { ListBodyDto } from 'svm-nest-lib-v3/dist/commons/dtos';
import { Connection, EntityRepository, In } from 'typeorm';
import {
  MySet,
  PayloadAGGridDto,
  convertFilterField,
  createSelectSql,
  createWhereSql,
  customSort,
  formatDate,
  handleResponse,
  isDoingGrouping,
  leadingZero,
  handleGetDataForAGGrid,
} from '../../../utils';

import {
  AuditEntity,
  AuditTimeTableStatus,
  AuditWorkspaceStatus,
  COMPANY_TYPE_NAME_FIXED,
  CONVERT_STATUS,
  CapStatusEnum,
  CarVerificationStatusEnum,
  CompanyLevelEnum,
  EmailTypeEnum,
  GlobalStatusEnum,
  InternalAuditReportStatus,
  MailTemplate,
  ModulePathEnum,
  PRStatusFilter,
  PlanningRequestStatus,
  PlanningTab,
  PlanningType,
  PushTypeEnum,
  ROLE_NAME_FIXED,
  SearchAvailabilityEnum,
  SelfAssessmentMonthEnum,
  WorkflowPermission,
} from '../../../commons/enums';
import APP_CONFIG from '../../../configs/app.config';
import { AuditWorkspace } from '../../audit-workspace/entities/audit-workspace.entity';
import { Company } from '../../company/company.entity';
import { ModuleType } from '../../user-assignment/user-assignment.enum';
import {
  AcceptPRBodyDTO,
  CheckAvailableAuditorsDTO,
  ListPRGraphicallyDTO,
  ListPRNeedReviewQueryDTO,
  ListPlanningRequestQueryDTO,
  PLANNING_FILTER_FIELDS,
  RejectPRBodyDTO,
  ReviewPRBodyDTO,
  SubmitPRQueryDTO,
  UpdatePlanningRequestDTO,
} from '../dto';
import { PlanningRequestHistory } from '../entities/planning-request-history.entity';
import { PlanningRequest } from '../entities/planning-request.entity';
import { PRAdditionalReviewer } from '../entities/pr-additional-reviewer.entity';
import { PROfficeComment } from '../entities/pr-office-comment.entity';
import { GraphicalGroupByEnum } from '../planning-request.enum';

import { cloneDeep } from 'lodash';
import { AppConst } from '../../../commons/consts/app.const';
import { CatalogConst } from 'src/modules-qa/catalog/catalog-key.const';
import { MetaConfig } from 'src/modules-qa/catalog/entity/meta-config.entity';
import { Department } from 'src/modules/department-master/department.entity';
import { DepartmentRepository } from 'src/modules/department-master/department.repository';
import { UserAssignment } from 'src/modules/user-assignment/user-assignment.entity';
import { RoleScopeEnum } from 'src/modules/user/enums/role-scope.enum';
import { getConnection } from 'typeorm';
import {
  _supportWhereDOCChartererOwner,
  _supportWhereDOCChartererOwnerRawQuery,
  decryptAttachmentValues,
  _supportCheckRoleScopeForGetList,
} from '../../../commons/functions';
import { CreatedUserHistoryModel } from '../../../commons/models';
import {
  EmailProducer,
  IEmailEventModel,
  IUserEmail,
} from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  NotificationProducer,
} from '../../../micro-services/async/notification.producer';
import { DateRangeDto } from '../../../modules-qa/summary/dto';
import { generatePointString } from '../../../utils/geo-location';
import { AuditChecklistRepository } from '../../audit-checklist/repository/audit-checklist.repository';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { AuditType } from '../../audit-type/audit-type.entity';
import { AuditWorkspaceRepository } from '../../audit-workspace/repositories/audit-workspace.repository';
import { CompanyFeatureVersionRepository } from '../../commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../commons/company-feature-version/feature-version.config';
import { UsageTypeEnum } from '../../company/company.enum';
import { CompanyRepository } from '../../company/company.repository';
import { SubscriptionUsage } from '../../company/subscription-usage.entity';
import { SubscriptionRepository } from '../../company/subscription.repository';
import { CARRepository } from '../../corrective-action-request/repositories/car.repository';
import { InternalAuditReportRepository } from '../../internal-audit-report/repositories/internal-audit-report.repository';
import { ListInspectionMapViewQueryDTO } from '../../map-view/dto/list-inspection-map-view.dto';
import { ListInspectorMapViewBodyDTO } from '../../map-view/dto/list-inspector-map-view.dto';
import { CreateUserAssignmentDTO, UserPermissionDTO } from '../../user-assignment/dto';
import { UserAssignmentRepository } from '../../user-assignment/user-assignment.repository';
import { User } from '../../user/user.entity';
import { UserRepository } from '../../user/user.repository';
import { VesselCharterer } from '../../vessel/entity/vessel-charterer.entity';
import { VesselDocHolder } from '../../vessel/entity/vessel-doc-holder.entity';
import { VesselOwner } from '../../vessel/entity/vessel-owner.entity';
import { VesselChartererRepository } from '../../vessel/repository/vessel-charterer.repository';
import { VesselDocHolderRepository } from '../../vessel/repository/vessel-doc-holder.repository';
import { VesselOwnerRepository } from '../../vessel/repository/vessel-owner.repository';
import { VesselRepository } from '../../vessel/vessel.repository';
import { DueDateAndDateOfLastInspectionDTO } from '../dto/due-date-and-date-of-last-inspection.dto';
import { PRAuditorsDto } from '../dto/pr-auditors.dto';
import { UpdatePRFocusRequestDTO } from '../dto/update-PR-focus-request.dto';
import { UpdatePRForGrantChartDTO } from '../dto/update-PR-grant-chart.dto';
import { PRDueDateAndDateOfLastInspection } from '../entities/pr-due-date-and-date-of-last-inspection.entity';
import { PRFocusRequest } from '../entities/pr-focus-request.entity';
import { PlanningRequestHistoryRepository } from './planning-request-history.repository';
import { PlanningRequestFocusRequestRepository } from './pr-focus-request.repository ';
import { PlanningRequestOfficeCommentRepository } from './pr-office-comment.repository';
import { InternalAuditReport } from '../../internal-audit-report/entities/internal-audit-report.entity';

@EntityRepository(PlanningRequest)
export class PlanningRequestRepository extends TypeORMRepository<PlanningRequest> {
  constructor(
    private readonly connection: Connection,
    private readonly notificationProducer: NotificationProducer,
    private readonly emailProducer: EmailProducer,
  ) {
    super();
  }

  async _migratePlanningRequestWithUserAssignment() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_PLANNING_REQUEST_USER_ASSIGNMENT,
      })
      .getOne();
    if (!metaConfig) {
      const userAssignments = await this.connection
        .getRepository(UserAssignment)
        .createQueryBuilder('user_assignment')
        .select(['user_assignment.id', 'user_assignment.userId'])
        .distinct(true)
        .where('user_assignment.deleted = false')
        .getMany();

      const dataList = await this.createQueryBuilder('planningRequest')
        .leftJoin('planningRequest.userAssignments', 'userAssignments')
        .addSelect(['userAssignments.id', 'userAssignments.userId', 'userAssignments.permission'])
        .where(`planningRequest.deleted = false`)
        .select()
        .getMany();

      const prHistories = await this.createQueryBuilder('planningRequest')
        .leftJoin('planningRequest.planningRequestHistories', 'prHistories')
        .where('planningRequest.id IN (:...planningRequestIds)', {
          planningRequestIds: dataList.map((i) => i.id),
        })
        .select([
          'planningRequest.id',
          'prHistories.status',
          'prHistories.createdAt',
          'prHistories.createdUser',
        ])
        .getMany();

      for (const data of dataList) {
        for (const prHistory of prHistories) {
          if (prHistory.id === data.id) {
            data.planningRequestHistories = prHistory.planningRequestHistories;
          }
        }
      }

      for (const userAssignment of userAssignments) {
        await this.filterPRByWorkflow(dataList, userAssignment.userId, true, userAssignment.id);
      }

      await this.connection
        .getRepository(MetaConfig)
        .createQueryBuilder('meta_config')
        .insert()
        .into(MetaConfig)
        .values([
          {
            key: CatalogConst.MIGRATE_PLANNING_REQUEST_USER_ASSIGNMENT,
            lastTimeSync: '2023-09-08T04:20:00.000z',
          },
        ])
        .execute();
      console.log('migration done!');
    }
  }

  async _migrationUpdateGlobalStatus() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_GLOBAL_STATUS_PLANNING_REQUEST,
      })
      .getOne();

    if (!metaConfig) {
      return await this.connection.transaction(async (manager) => {
        await this._updateGlobalStatus(manager, true);

        await manager.save(MetaConfig, {
          key: CatalogConst.MIGRATE_GLOBAL_STATUS_PLANNING_REQUEST,
          lastTimeSync: '2024-01-29T14:20:00.000z',
        });
        console.log('Migrate done');
      });
    }

    return 1;
  }

  async filterPRByWorkflow(
    planningRequests,
    userId: string,
    isMigrate = false,
    userAssignmentId = null,
    isCheckViewPermission = true,
  ) {
    const filteredPr = [];
    const planingRequestIds = [];
    const planningRequestEditIds = [];
    for (const planningRequest of planningRequests) {
      let isUserApproverRejected = false;
      let statusUserReview = PlanningRequestStatus.SUBMITTED;
      let previousStatusReassign = PlanningRequestStatus.SUBMITTED;
      if (!planningRequest.userAssignments?.length) {
        continue;
      }

      const userApproved = planningRequest.userAssignments.find(
        (user) => user.permission === WorkflowPermission.APPROVER,
      );

      const listPRhistories = planningRequest.planningRequestHistories;

      const userApprover = listPRhistories?.filter(
        (value) => value?.createdUser?.id === userApproved?.userId && value.status === 'rejected',
      );

      const listUserReviews = listPRhistories?.filter((user) => user.status.includes('reviewed'));

      if (listUserReviews?.length) {
        const lastUserReview = listUserReviews.reduce((max, current) => {
          if (current.status.localeCompare(max.status) > 0) {
            return current;
          } else {
            return max;
          }
        });

        statusUserReview = lastUserReview.status;
      }
      const nextReviewer = this._getNextUserReview(
        planningRequest?.userAssignments,
        planningRequest.status,
      );
      if (listPRhistories) {
        previousStatusReassign = listPRhistories.length
          ? this._getPreviousReassignStatus(listPRhistories, nextReviewer)
          : previousStatusReassign;
      }

      if (userApprover?.length) isUserApproverRejected = true;

      let viewPermission = false;
      let isEdit = false;
      switch (planningRequest.status) {
        case PlanningRequestStatus.DRAFT:
          viewPermission = planningRequest.userAssignments.some(
            (ua) => ua.userId === userId && ua.permission === WorkflowPermission.CREATOR,
          );
          isEdit = viewPermission;
          break;
        case PlanningRequestStatus.SUBMITTED:
          viewPermission = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1),
          );
          isEdit = viewPermission;
          break;
        case PlanningRequestStatus.REJECTED:
          if (statusUserReview === PlanningRequestStatus.SUBMITTED) {
            viewPermission = planningRequest.userAssignments.some(
              (ua) => ua.userId === userId && ua.permission === WorkflowPermission.CREATOR,
            );
            isEdit = viewPermission;
          }

          // Check edit permission

          //reviewer 1 reassign
          if (previousStatusReassign === PlanningRequestStatus.REVIEWED_1) {
            isEdit = planningRequest.userAssignments.some(
              (ua) => ua.userId === userId && ua.permission === WorkflowPermission.CREATOR,
            );
            viewPermission = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1),
            );
          }
          //reviewer 2 reassign
          if (previousStatusReassign === PlanningRequestStatus.REVIEWED_2) {
            viewPermission = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2),
            );
            isEdit = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1),
            );
          }
          //reviewer 3 reassign
          if (previousStatusReassign === PlanningRequestStatus.REVIEWED_3) {
            viewPermission = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2 ||
                  ua.permission === WorkflowPermission.REVIEWER3),
            );
            isEdit = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER2),
            );
          }
          //reviewer 4 reassign
          if (previousStatusReassign === PlanningRequestStatus.REVIEWED_4) {
            viewPermission = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2 ||
                  ua.permission === WorkflowPermission.REVIEWER3 ||
                  ua.permission === WorkflowPermission.REVIEWER4),
            );
            isEdit = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER3),
            );
          }
          //reviewer 5 reassign
          if (previousStatusReassign === PlanningRequestStatus.REVIEWED_5) {
            viewPermission = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER1 ||
                  ua.permission === WorkflowPermission.REVIEWER2 ||
                  ua.permission === WorkflowPermission.REVIEWER3 ||
                  ua.permission === WorkflowPermission.REVIEWER4 ||
                  ua.permission === WorkflowPermission.REVIEWER5),
            );
            isEdit = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === WorkflowPermission.REVIEWER4),
            );
          }
          //approved reassign
          if (previousStatusReassign === PlanningRequestStatus.APPROVED) {
            viewPermission = planningRequest.userAssignments.some(
              (ua) => ua.userId === userId && this._checkUserHaveFullPermission(ua.permission),
            );

            isEdit = planningRequest.userAssignments.some(
              (ua) =>
                ua.userId === userId &&
                (ua.permission === WorkflowPermission.CREATOR ||
                  ua.permission === this._comparePermissionAndStatus(statusUserReview)),
            );
          }
          break;

        case PlanningRequestStatus.REVIEWED_1:
          viewPermission = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          break;
        case PlanningRequestStatus.REVIEWED_2:
          viewPermission = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          break;

        case PlanningRequestStatus.REVIEWED_3:
          viewPermission = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                ua.permission === WorkflowPermission.REVIEWER4 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER4 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          break;
        case PlanningRequestStatus.REVIEWED_4:
          viewPermission = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER1 ||
                ua.permission === WorkflowPermission.REVIEWER2 ||
                ua.permission === WorkflowPermission.REVIEWER3 ||
                ua.permission === WorkflowPermission.REVIEWER4 ||
                ua.permission === WorkflowPermission.REVIEWER5 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.REVIEWER5 ||
                (!nextReviewer.length && ua.permission === WorkflowPermission.APPROVER)),
          );
          break;

        case PlanningRequestStatus.REVIEWED_5:
          viewPermission = planningRequest.userAssignments.some(
            (ua) => ua.userId === userId && this._checkUserHaveFullPermission(ua.permission),
          );
          isEdit = planningRequest.userAssignments.some(
            (ua) =>
              ua.userId === userId &&
              (ua.permission === WorkflowPermission.CREATOR ||
                ua.permission === WorkflowPermission.APPROVER),
          );
          break;
        case PlanningRequestStatus.APPROVED:
          viewPermission = true;
          isEdit = planningRequest.userAssignments.some(
            (ua) => ua.userId === userId && ua.permission === WorkflowPermission.CREATOR,
          );
          break;
        case PlanningRequestStatus.IN_PROGRESS:
        case PlanningRequestStatus.COMPLETED:
          viewPermission = true;
          isEdit = false;
          break;
        default:
      }

      if (viewPermission && isMigrate && userAssignmentId) {
        planingRequestIds.push(planningRequest.id);
      }

      if (isEdit && isMigrate && userAssignmentId) {
        planningRequestEditIds.push(planningRequest.id);
      }
      if (!isCheckViewPermission || (isCheckViewPermission && viewPermission)) {
        filteredPr.push({ ...planningRequest, isEdit });
      }
    }
    if (planingRequestIds.length || planningRequestEditIds.length) {
      await this.connection.transaction(async (managerTrans) => {
        await this.updateAssignment(
          managerTrans,
          userAssignmentId,
          planingRequestIds,
          true,
          userId,
          false,
        );
        await this.updateAssignment(
          managerTrans,
          userAssignmentId,
          planningRequestEditIds,
          false,
          userId,
          true,
        );
      });
    }

    return filteredPr;
  }

  async updateAssignment(
    managerTrans: any,
    userAssignmentId: string,
    planningRequestIds: string[],
    isView: boolean,
    userId: string,
    isEdit: boolean,
  ) {
    if (planningRequestIds.length) {
      if (isView) {
        await managerTrans.update(
          UserAssignment,
          {
            id: userAssignmentId,
            planningRequestId: In(planningRequestIds),
            userId,
          },
          { isView },
        );
      }

      if (isEdit) {
        await managerTrans.update(
          UserAssignment,
          {
            id: userAssignmentId,
            planningRequestId: In(planningRequestIds),
            userId,
          },
          { isEdit },
        );
      }
    }
  }

  async checkAvailableAuditors(params: CheckAvailableAuditorsDTO) {
    try {
      if (params.listAuditorId.length == 0) {
        return [];
      }

      const userIdList = params.listAuditorId.map((id) => `'${id}'`);
      const rawQuery = `SELECT
                        DISTINCT pra."userId"
                        FROM
                          planning_request_auditor pra
                        JOIN planning_request pr ON pr.id = pra."planningRequestId"
                        WHERE
                          pra."userId" IN (${userIdList})
                          AND ($2 >= pr."plannedFromDate"
                            AND $1 <= pr."plannedToDate")
                          AND pr."status" IN ('${PlanningRequestStatus.APPROVED}', '${PlanningRequestStatus.IN_PROGRESS}')
                        UNION
                        SELECT
                          DISTINCT ito."offUserId"
                        FROM
                          inspector_time_off ito
                        WHERE
                          ito."offUserId" IN (${userIdList})
                          AND ($2 >= ito."startDate"
                            AND $1 <= ito."endDate")
                          AND ito.deleted = FALSE`;

      const data = await this.manager.query(rawQuery, [params.fromDate, params.toDate]);

      const unAvailableUserIds = data.map((item) => item.userId);

      return unAvailableUserIds;
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] checkAvailableAuditors error: ',
        ex.message || ex,
      );
    }
  }

  async createPR(
    planningRequestParams: PlanningRequest,
    focusRequestParams: PRFocusRequest[],
    PROfficeCommentParams: PROfficeComment[],
    token: TokenPayloadModel,
    userAssignmentParams?: CreateUserAssignmentDTO,
    dueDateAndDateOfLastInspections?: DueDateAndDateOfLastInspectionDTO[],
  ) {
    try {
      const res = await this.connection.transaction(async (manager) => {
        const dataNoti: INotificationEventModel[] = [];
        const dataMail: IEmailEventModel[] = [];
        const remanderMail: IEmailEventModel[] = [];
        const currYear = momentTZ.tz(planningRequestParams.timezone).year();
        const counter = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager: manager,
            companyId: token.companyId,
            feature: FeatureVersionConfig.PLANNING_REQUEST_COUNTER,
            year: Number(currYear),
          });

        const company = await manager.findOne(Company, {
          where: { id: token.companyId },
          select: ['code'],
        });
        // Get active subscription
        const companyId = token.parentCompanyId || token.companyId;

        // Get subscription active
        const subscription = await manager
          .getCustomRepository(SubscriptionRepository)
          .getSubscriptionActive(companyId);

        if (!subscription) {
          throw new BaseError({ message: 'subscription.NOT_HAVE_SUBSCRIPTION' });
        }

        // Check current counter jobs number
        const currentJobs = await manager.findOne(SubscriptionUsage, {
          where: {
            companyId,
            type: UsageTypeEnum.JOBS,
          },
          lock: {
            mode: 'pessimistic_write',
          },
        });

        // Check if current jobs have reached the limit if reached send to intimation mail for admin
        const limit = Math.round((subscription.numJobs / 100) * 5);
        const reachedLimit = subscription.numJobs - limit;
        if (currentJobs.counter + 1 === reachedLimit) {
          const userFoundByEmail = await this.connection
            .getRepository(User)
            .createQueryBuilder('user')
            .where('user.companyId = :companyId', { companyId: companyId })
            .andWhere('user.roleScope = :roleScope AND user.deleted = false', {
              roleScope: RoleScopeEnum.ADMIN,
            })
            .select()
            .getOne();
          remanderMail.push({
            receiver: {
              email: userFoundByEmail.email,
            },
            subject: 'Reminder: Jobs Creation Limit Nearing',
            type: EmailTypeEnum.JOBS_LIMIT_REACHED,
            templateKey: MailTemplate.JOBS_LIMIT_REACHED,
            data: {
              userName: userFoundByEmail.username,
              totalJobs: subscription.numJobs,
              currentJobs: currentJobs.counter + 1,
              logoImage: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.CDN_RESOURCE + AppConst.LOGO_IMAGE,
            },
          });
        }

        // check logic
        if (currentJobs.counter + 1 > subscription.numJobs) {
          throw new BaseError({ message: 'subscription.LIMIT_JOBS' });
        }

        await manager.update(
          SubscriptionUsage,
          {
            companyId,
            type: UsageTypeEnum.JOBS,
          },
          { counter: currentJobs.counter + 1 },
        );
        const serialNumber = leadingZero(counter, 3);
        if (planningRequestParams.vesselId) {
          const vesselDetail = await this.manager
            .getCustomRepository(VesselRepository)
            .getDetailVesselById('', planningRequestParams.vesselId);
          // console.log(vesselDetail.vesselDocHolders);
          if (vesselDetail) {
            if (vesselDetail.vesselDocHolders.length > 0) {
              Object.assign(planningRequestParams, {
                docHolderCurrentCompanyId: vesselDetail.vesselDocHolders[0].companyId,
              });
            }

            if (vesselDetail.vesselCharterers.length > 0) {
              Object.assign(planningRequestParams, {
                vesselChartererCurrentCompanyId: vesselDetail.vesselCharterers[0].companyId,
              });
            }

            if (vesselDetail.vesselOwners.length > 0) {
              Object.assign(planningRequestParams, {
                vesselOwnerCurrentCompanyId: vesselDetail.vesselOwners[0].companyId,
              });
            }
          }
        }
        // console.log('planningRequestParams', planningRequestParams);
        const monthList = Object.keys(SelfAssessmentMonthEnum).map(
          (key) => SelfAssessmentMonthEnum[key],
        );
        // const submittedDate = new Date();
        const { plannedFromDate, plannedToDate } = planningRequestParams;

        const planningRequest = await manager.save(
          PlanningRequest,
          Object.assign(planningRequestParams, {
            refId: `${company.code}/PL/${serialNumber}/${currYear}`,
            auditNo: `PL${company.code}${currYear}${serialNumber}`,
            globalStatus: GlobalStatusEnum.OPENING_SCHEDULE,
            plannedFromDate_Month: plannedFromDate ? monthList[plannedFromDate.getMonth()] : null,
            plannedFromDate_Year: plannedFromDate ? plannedFromDate.getFullYear() : null,
            plannedToDate_Month: plannedToDate ? monthList[plannedToDate.getMonth()] : null,
            plannedToDate_Year: plannedToDate ? plannedToDate.getFullYear() : null,
          }),
        );
        if (dueDateAndDateOfLastInspections?.length) {
          await manager.save(PRDueDateAndDateOfLastInspection, dueDateAndDateOfLastInspections);
        }

        // Get user info
        const createdUser = await manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);

        // prepare PR office commment user created
        const preparedPROfficeCommentParams: PROfficeComment[] = [];
        for (let i = 0; i < PROfficeCommentParams.length; i++) {
          preparedPROfficeCommentParams.push({
            ...PROfficeCommentParams[i],
            createdUser,
          });
        }

        await manager.insert(PlanningRequestHistory, {
          planningRequestId: planningRequest.id,
          remark: null,
          status: PlanningRequestStatus.DRAFT,
          createdUser,
        });

        await manager.save(PROfficeComment, preparedPROfficeCommentParams);

        await manager.save(PRFocusRequest, focusRequestParams);

        const auditLogActivities: AuditActivityEnum[] = [AuditActivityEnum.CREATED];
        if (planningRequest.status === PlanningRequestStatus.DRAFT) {
          const params: CreateUserAssignmentDTO = {
            planningRequestId: planningRequest.id,
            usersPermissions: [
              {
                permission: WorkflowPermission.CREATOR,
                isView: true,
                isEdit: true,
                userIds: [token.id],
              },
            ],
          };

          await manager.getCustomRepository(UserAssignmentRepository).createUserAssignment(params);
        }

        if (planningRequest.status === PlanningRequestStatus.SUBMITTED) {
          if (!userAssignmentParams) {
            throw new BaseError({ message: 'userAssignment.REQUIRED' });
          }

          // Create user assignment when submit
          await manager
            .getCustomRepository(UserAssignmentRepository)
            .createUserAssignment(userAssignmentParams);

          //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager
          // list User Assignment
          const listUserAssignment = await manager
            .getCustomRepository(UserAssignmentRepository)
            .listByModule(ModuleType.PLANNING_REQUEST, planningRequest.id);

          let listReceiverNotiAndMail = [];
          listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
            listUserAssignment[WorkflowPermission.REVIEWER1],
          );

          // listReceiverNoti = listReceiverNoti.concat(
          //   listUserAssignment[WorkflowPermission.AUDITOR],
          // );
          // listReceiverNoti = listReceiverNoti.concat(
          //   listUserAssignment[WorkflowPermission.OWNER_MANAGER],
          // );

          const performer = await manager
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle']);
          dataNoti.push({
            receivers: listReceiverNotiAndMail,
            module: ModuleType.PLANNING_REQUEST,
            recordId: planningRequest.id,
            recordRef: planningRequest.refId,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            currentStatus: PlanningRequestStatus.SUBMITTED,
            previousStatus: PlanningRequestStatus.DRAFT,
            performer: performer,
            executedAt: new Date(),
          });

          for (const receiver of listReceiverNotiAndMail) {
            dataMail.push({
              receiver: {
                email: receiver.email,
              },
              type: EmailTypeEnum.CHANGE_RECORD_STATUS,
              templateKey: MailTemplate.CHANGE_RECORD_STATUS,
              subject: '[Notification] Change status in a record',
              data: {
                username: receiver.username,
                baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                recordRef: planningRequest.refId,
                recordId: planningRequest.id,
                path: ModulePathEnum.PLANNING_AND_REQUEST,
                currentStatus: PlanningRequestStatus.SUBMITTED,
                previousStatus: PlanningRequestStatus.DRAFT,
              },
            });
          }

          //#endregion Push noti
          // Create PR history: DRAFT
          await manager.insert(PlanningRequestHistory, {
            planningRequestId: planningRequest.id,
            remark: null,
            status: PlanningRequestStatus.SUBMITTED,
            createdUser,
          });

          auditLogActivities.push(AuditActivityEnum.SUBMITTED);
        }

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: planningRequest.id,
          },
          token,
          auditLogActivities,
          createdUser,
        );

        return { dataNoti, dataMail, remanderMail };
      });
      return res;
    } catch (ex) {
      LoggerCommon.error('[PlanningRequestRepository] createPR error ', ex.message || ex);
      throw ex;
    }
  }

  buildSql(body: PayloadAGGridDto, queryBuilder, token, subQueryBuilder, fieldSelects) {
    if (body) {
      convertFilterField(body, PLANNING_FILTER_FIELDS);

      // const queryDocHolder = this.createQueryBuilder('planningRequest')
      //   .leftJoin('planningRequest.auditCompany', 'auditCompany')
      //   .leftJoin('planningRequest.vessel', 'vessel')
      //   .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      //   .leftJoin(
      //     'vesselDocHolders.company',
      //     'companyVesselDocHolders',
      //     `vesselDocHolders.status = 'active'`,
      //   )
      //   .select('planningRequest.id')
      //   .addSelect([
      //     `STRING_AGG(DISTINCT "companyVesselDocHolders"."name", ', ') AS "holderCompany"`,
      //   ])
      //   .groupBy(`planningRequest.id`);

      // const queryDepartment = this.createQueryBuilder('planningRequest')
      //   .leftJoin('planningRequest.departments', 'departments')
      //   .select('planningRequest.id')
      //   .addSelect(`STRING_AGG(DISTINCT departments.name, ', ') AS "departmentsName"`)
      //   .groupBy(`planningRequest.id`);

      // const queryAuditTypes = this.createQueryBuilder('planningRequest')
      //   .leftJoin('planningRequest.auditTypes', 'auditTypes')
      //   .select('planningRequest.id')
      //   .addSelect(`STRING_AGG(DISTINCT auditTypes.name, ', ') AS "auditTypesName"`)
      //   .groupBy(`planningRequest.id`);

      // const queryAuditors = this.createQueryBuilder('planningRequest')
      //   .leftJoin('planningRequest.auditors', 'auditors')
      //   .select('planningRequest.id')
      //   .addSelect(`STRING_AGG(DISTINCT auditors.username, ', ') AS "nameOfInspector"`)
      //   .groupBy(`planningRequest.id`);

      // queryBuilder
      //   .innerJoin(
      //     `(${queryDocHolder.getQuery()})`,
      //     'holderCompanyGroup',
      //     `"holderCompanyGroup"."planningRequest_id" = "planningRequest"."id"`,
      //   )
      //   .innerJoin(
      //     `(${queryDepartment.getQuery()})`,
      //     'departmentsGroup',
      //     `"departmentsGroup"."planningRequest_id" = "planningRequest"."id"`,
      //   )
      //   .innerJoin(
      //     `(${queryAuditTypes.getQuery()})`,
      //     'auditTypesGroup',
      //     `"auditTypesGroup"."planningRequest_id" = "planningRequest"."id"`,
      //   )
      //   .innerJoin(
      //     `(${queryAuditors.getQuery()})`,
      //     'auditorsGroup',
      //     `"auditorsGroup"."planningRequest_id" = "planningRequest"."id"`,
      //   );

      queryBuilder
        .select()
        .addSelect(fieldSelects)
        // .addSelect(`STRING_AGG(DISTINCT auditors.username, ', ') AS "nameOfInspector"`)
        // .addSelect(`"auditorsGroup"."nameOfInspector"`)
        // .addSelect(`"auditTypesGroup"."auditTypesName"`)
        // .addSelect(`"departmentsGroup"."departmentsName"`)
        // .addSelect(`"holderCompanyGroup"."holderCompany"`)pop
        .addSelect(`DATE_PART('year', "plannedFromDate") AS "plannedFromDateYear"`)
        .addSelect(`TO_CHAR("plannedFromDate", 'Mon') AS "plannedFromDateMonth"`)
        .addSelect(`DATE_PART('year', "plannedToDate") AS "plannedToDateYear"`)
        .addSelect(`TO_CHAR("plannedToDate", 'Mon') AS "plannedToDateMonth"`)
        .andWhere(`planningRequest.deleted = false`)
        .groupBy(
          `planningRequest.id, 
          ${fieldSelects.join(', ')}`,
        );

      subQueryBuilder
        .select(`DISTINCT "planningRequest_id" AS id`)
        .from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"planningRequest_id"');

      for (const item of body.rowGroupCols) {
        if (item.id === 'auditCompany') {
          subQueryBuilder
            .andWhere(`"companyVesselDocHolders_name" IS NOT NULL`);
          break;
        }
        if (item.id === 'officeName') {
          subQueryBuilder
            .andWhere(`"auditCompany_name" IS NOT NULL`);
          break;
        }
      }
    }

    queryBuilder
      .leftJoin('planningRequest.departments', 'departments')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .select('planningRequest')
      .addSelect(fieldSelects)
      .addSelect([
        'auditors.id',
        'auditors.employeeId',
        'auditors.username',
        'companyVesselDocHolders.name',
        'auditTimeTable.actualFrom',
        'auditTimeTable.actualTo',
        'userAssignments.id',
        'userAssignments.userId',
        'userAssignments.permission',
        'userAssignments.isEdit',
        'user.id',
        'user.username',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
      ])
      .groupBy();
  }

  async listPlanningRequest(
    query: ListPlanningRequestQueryDTO,
    token: TokenPayloadModel,
    body?: PayloadAGGridDto,
    isCounting?: boolean,
  ) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
      .leftJoin('vessel.country', 'country')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin(
        'vesselDocHolders.company',
        'companyVesselDocHolders',
        `vesselDocHolders.status = 'active'`,
      )
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .leftJoin('planningRequest.location', 'location')
      .leftJoin('planningRequest.prDueDateAndDateOfLastInspections', 'prDueDateAndDateOfLastAudits')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      .leftJoin('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.company', 'company')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('planningRequest.voyageType', 'voyageType')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      .leftJoin('divisionMapping.division', 'division')
      .leftJoin('division.users', 'users')
      .where(`(planningRequest.companyId = '${token.companyId}')`)
      .andWhere('planningRequest.deleted = false');

    if (query.content) {
      queryBuilder.andWhere(`(planningRequest.refId ILIKE '%${query.content}%')`);
    }
    if (query.pageSize != -1 && body?.pivotCols?.length) {
      queryBuilder.distinctOn(['planningRequest.id']);
    }
    if (query.planningFrom && query.planningTo && query.isInspectorSchedule) {
      queryBuilder.andWhere(
        `((planningRequest.plannedFromDate <= '${formatDate(query.planningTo)}') ` +
          `  AND (planningRequest.plannedToDate >= '${formatDate(query.planningFrom)}')) `,
      );
    }
    if (query.planningFrom && query.planningTo && !query.isInspectorSchedule) {
      queryBuilder.andWhere(
        `planningRequest.plannedFromDate >= '${formatDate(
          query.planningFrom,
        )}' AND planningRequest.plannedToDate <='${formatDate(query.planningTo)}'`,
      );
    }

    if (query.status) {
      queryBuilder.andWhere(`planningRequest.status = '${query.status}'`);
    }
    if (query.vesselId) {
      queryBuilder.andWhere(`planningRequest.vesselId = '${query.vesselId}'`);
    }

    if (query.isInspectionHistory) {
      queryBuilder.andWhere(
        `planningRequest.status IN ('${PlanningRequestStatus.APPROVED}','${
          PlanningRequestStatus.IN_PROGRESS
        }','${PlanningRequestStatus.COMPLETED}','${
          PlanningRequestStatus.CANCELLED
        }') AND planningRequest.plannedFromDate < '${formatDate()}' AND planningRequest.plannedToDate < '${formatDate()}'`,
      );
    }

    if (query.departmentId) {
      queryBuilder.andWhere(`departments.id = '${query.departmentId}'`);
    }
    // IMPORTANT: Search PR overlaps with given date range
    if (query.fromDate) {
      queryBuilder.andWhere(`planningRequest.plannedToDate >= '${formatDate(query.fromDate)}'`);
    }

    if (query.toDate) {
      queryBuilder.andWhere(`planningRequest.plannedFromDate <= '${query.toDate}'`);
    }

    if (query.entityType) {
      queryBuilder.andWhere(`planningRequest.entityType = '${query.entityType}'`);
    }

    if (query.auditCompanyId) {
      queryBuilder.andWhere(`planningRequest.auditCompanyId = '${query.auditCompanyId}'`);
    }
    if (query.isSafetyEngagementType) {
      queryBuilder.andWhere(`LOWER(auditTypes.name) LIKE 'safety engagement'`);
    }

    if (!query.ids?.length && !RoleScopeCheck.isAdmin(token)) {
      queryBuilder
        .andWhere('userAssignments.isView =  true')
        .andWhere(
          `(CASE WHEN planningRequest.status = 'draft' THEN planningRequest.createdUserId = '${token.id}' ELSE true END)`,
        );

      // eslint-disable-next-line prefer-const
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        token.explicitCompanyId,
        'planningRequest',
      );
      const isGetList = query.entityType && query.entityType === AuditEntity.VESSEL;
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        token,
        whereForMainAndInternal,
        whereForExternal,
        'planningRequest',
        false,
        isGetList,
      );
    }

    const fieldSelects = [
      // 'createdUser.username',
      // 'updatedUser.username',
      'fromPort.id',
      'fromPort.name',
      'fromPort.code',
      'toPort.id',
      'toPort.name',
      'toPort.code',
      'vessel.id',
      'vessel.name',
      'vessel.code',
      'vessel.countryId',
      'vessel.country',
      'country.id',
      'country.name',
      'vessel.vesselType',
      'vesselType.id',
      'vesselType.name',
      'leadAuditor.id',
      'leadAuditor.employeeId',
      'leadAuditor.username',
      'company.id',
      'company.name',
      'location.id',
      'location.name',
      'auditCompany.name',
      'prDueDateAndDateOfLastAudits.dateOfLastInspection',
      'prDueDateAndDateOfLastAudits.dueDate',
      'companyVesselDocHolders.name',
      'voyageType.name',
    ];

    //for homepage
    if (query.ids?.length) {
      queryBuilder.andWhere(`"planningRequest"."id" IN (${query.ids.map((id) => `'${id}'`)})`);

      if (RoleScopeCheck.isAdmin(token)) {
        fieldSelects.push(
          // 'userAssignments.userId',
          // 'vessel.id',
          'vesselCharterers.id',
          'vesselCharterers.companyId',
          'vesselCharterers.fromDate',
          'vesselCharterers.toDate',
          'vesselOwners.id',
          'vesselOwners.companyId',
          'vesselOwners.fromDate',
          'vesselOwners.toDate',
        );
      }

      // return await this._supportOptimizeListPrForHomepage(queryBuilder, query);
    }

    const connection = getConnection();
    const subQueryBuilder = connection.createQueryBuilder();

    // this.buildSql(body, queryBuilder, token, subQueryBuilder, fieldSelects);

    switch (query.tab) {
      case PlanningTab.PLANNING:
        queryBuilder.andWhere(
          `planningRequest.status NOT IN ('${PlanningRequestStatus.COMPLETED}', '${PlanningRequestStatus.CANCELLED}') AND auditors.id IS NOT NULL`,
        );
        // subQueryBuilder.andWhere(
        //   `"planningRequest_status" NOT IN ('${PlanningRequestStatus.COMPLETED}', '${PlanningRequestStatus.CANCELLED}') AND "nameOfInspector" IS NOT NULL`,
        // );
        break;
      case PlanningTab.COMPLETED:
        queryBuilder.andWhere(
          `planningRequest.status = '${PlanningRequestStatus.COMPLETED}' AND auditors.id IS NOT NULL`,
        );
        // subQueryBuilder.andWhere(
        //   `"planningRequest_status" = '${PlanningRequestStatus.COMPLETED}' AND "nameOfInspector" IS NOT NULL`,
        // );
        break;
      case PlanningTab.CANCELLED:
        queryBuilder.andWhere(
          `planningRequest.status = '${PlanningRequestStatus.CANCELLED}' AND auditors.id IS NOT NULL`,
        );
        // subQueryBuilder.andWhere(
        //   `"planningRequest_status" = '${PlanningRequestStatus.CANCELLED}' AND "nameOfInspector" IS NOT NULL`,
        // );
        break;
      case PlanningTab.UNPLANNED:
        queryBuilder.andWhere(
          `(auditors.id IS NULL OR (planningRequest.plannedFromDate IS NULL OR planningRequest.plannedToDate IS NULL))`,
        );
        // subQueryBuilder.andWhere(
        //   `("nameOfInspector" IS NULL OR ("planningRequest_plannedFromDate" IS NULL OR "planningRequest_plannedToDate" IS NULL))`,
        // );
        break;
      case PlanningTab.INSPECTOR_SCHEDULER:
        queryBuilder.andWhere(
          `planningRequest.status IN ('${PlanningRequestStatus.APPROVED}', '${PlanningRequestStatus.IN_PROGRESS}')`,
        );
        // subQueryBuilder.andWhere(
        //   `"planningRequest_status" IN ('${PlanningRequestStatus.APPROVED}', '${PlanningRequestStatus.IN_PROGRESS}')`,
        // );
        break;
      default:
    }
    this.buildSql(body, queryBuilder, token, subQueryBuilder, fieldSelects);
    if (isCounting) {
      return queryBuilder.getCount();
    }

    const subQuerySelect = [
      `"plannedFromDateYear"`,
      `"plannedFromDateMonth"`,
      `"plannedToDateYear"`,
      `"plannedToDateMonth"`,
    ];
    const queryBuilderNew = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('planningRequest.voyageType', 'voyageType')
      .leftJoin('vessel.country', 'country')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      .leftJoin('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.company', 'company')
      .leftJoin('planningRequest.location', 'location')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
      .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin('planningRequest.prDueDateAndDateOfLastInspections', 'prDueDateAndDateOfLastAudits')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin(
        'vesselDocHolders.company',
        'companyVesselDocHolders',
        `vesselDocHolders.status = 'active'`,
      )
      .addSelect(fieldSelects);

    let dataList = await handleGetDataForAGGrid(
      this,
      queryBuilder,
      query,
      body,
      subQueryBuilder,
      queryBuilderNew,
      'planningRequest',
      subQuerySelect,
    );

    if (dataList.data?.length === 0) {
      return dataList;
    }

    if (body) {
      if (isDoingGrouping(body)) {
        return dataList;
      }
      const dataIds: string[] = dataList.data.map((pr) => pr.id);
      const queryUserAssignments = this
        // .getCustomRepository(UserAssignmentRepository)
        .createQueryBuilder('planningRequest')
        .leftJoin('planningRequest.userAssignments', 'userAssignments')
        // .leftJoin('userAssignments.planningRequest', 'planningRequest')
        .leftJoin('userAssignments.user', 'user')
        .where(`planningRequest.id IN (${dataIds.map((id) => `'${id}'`)})`)
        .select([
          'planningRequest.id',
          'userAssignments.id',
          'userAssignments.userId',
          'userAssignments.permission',
          'userAssignments.isEdit',
          'user.id',
          'user.username',
        ]);
      if (!RoleScopeCheck.isAdmin(token)) {
        queryUserAssignments.andWhere('userAssignments.isView =  true');
      }
      const userAssignments = await queryUserAssignments.getMany();
      for (const data of dataList.data) {
        data.userAssignments = userAssignments.find(
          (userAssignment) => userAssignment.id === data.id,
        )?.userAssignments;
        data.plannedFromDate_Year = data.plannedFromDateYear;
        data.plannedFromDate_Month = data.plannedFromDateMonth;
        data.plannedToDate_Year = data.plannedToDateYear;
        data.plannedToDate_Month = data.plannedToDateMonth;
        data.dueDate = data.prDueDateAndDateOfLastInspections[0]?.dueDate;
        data.dateOfLastInspection = data.prDueDateAndDateOfLastInspections[0]?.dateOfLastInspection;
      }
    }

    let planningRequestData = cloneDeep(dataList.data);

    const vesselIds: string[] = [];
    const planningRequestIds: string[] = [];
    for (const data of planningRequestData) {
      const userAssigns = data.userAssignments?.filter(
        (userAssignment) => userAssignment.userId === token.id && userAssignment.isEdit,
      );
      if (userAssigns?.length) data['isEdit'] = true;
      planningRequestIds.push(data.id);
      if (data.vesselId) {
        vesselIds.push(data.vesselId);
      }
    }

    const queryPRHistory = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.planningRequestHistories', 'prHistories')
      .where('planningRequest.id IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .select([
        'planningRequest.id',
        'prHistories.status',
        'prHistories.createdAt',
        'prHistories.createdUser',
      ])
      .orderBy('"prHistories"."createdAt"', 'DESC');

    const queryAuditType = this.connection
      .getCustomRepository(PlanningRequestRepository)
      .createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .where('planningRequest.id IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .select(['planningRequest.id', 'auditTypes.id', 'auditTypes.name']);

    const queryInternalAuditReport = this.connection
      .getCustomRepository(InternalAuditReportRepository)
      .createQueryBuilder('internalAuditReport')
      .leftJoin(
        'internalAuditReport.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .where('internalAuditReport.planningRequestId IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .select([
        'internalAuditReport.id',
        'internalAuditReport.planningRequestId',
        'reportFindingItems.id',
        'reportFindingItems.findingStatus',
      ]);
    const queryVesselOwner = this.connection
      .getCustomRepository(VesselRepository)
      .createQueryBuilder('vessel')
      .leftJoin('vessel.owners', 'owners')
      .select(['vessel.id', 'owners.id', 'owners.username']);
    if (vesselIds.length > 0) {
      queryVesselOwner.andWhere('vessel.id IN (:...vesselIds)', {
        vesselIds,
      });
    } else {
      queryVesselOwner.andWhere('1=0');
    }
    const queryDepartments = this.connection
      .getCustomRepository(PlanningRequestRepository)
      .createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.departments', 'departments')

      .where('planningRequest.id IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .select(['planningRequest.id', 'departments.id', 'departments.name']);

    const queryAuditor = this.connection
      .getCustomRepository(PlanningRequestRepository)
      .createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .where('planningRequest.id IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .select(['planningRequest.id', 'auditors.id', 'auditors.employeeId', 'auditors.username']);

    const queryVesselDocHolders = this.connection
      .getCustomRepository(PlanningRequestRepository)
      .createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .where('planningRequest.id IN (:...planningRequestIds)', {
        planningRequestIds,
      })
      .select([
        'planningRequest.id',
        'vessel.id',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
      ]);

    const [
      auditTypes,
      planningRequestAdditionalReviewers,
      departments,
      vesselOwners,
      internalAuditReports,
      prHistories,
      auditors,
      vesselDocHolders,
    ] = await Promise.all([
      queryAuditType.getMany(),
      this.manager.find(PRAdditionalReviewer, {
        where: { planningRequestId: In(planningRequestIds) },
        select: ['id', 'comment', 'planningRequestId'],
      }),
      queryDepartments.getMany(),
      queryVesselOwner.getMany(),
      queryInternalAuditReport.getMany(),
      queryPRHistory.getMany(),
      queryAuditor.getMany(),
      queryVesselDocHolders.getMany(),
    ]);
    // console.log('planningRequestAdditionalReviewers', planningRequestAdditionalReviewers);
    for (const data of planningRequestData) {
      data.auditTypes = [];
      data.planningRequestAdditionalReviewers = [];
      data.departments = [];
      data.planningRequestHistories = [];
      data.planningRequestAdditionalReviewers = planningRequestAdditionalReviewers.filter(
        (planningRequestAdditionalReviewer) =>
          data.id === planningRequestAdditionalReviewer.planningRequestId,
      );
      for (const auditType of auditTypes) {
        if (auditType.id === data.id) {
          data.auditTypes = auditType.auditTypes;
        }
      }
      for (const department of departments) {
        if (department.id === data.id) {
          data.departments = department.departments;
        }
      }
      for (const owner of vesselOwners) {
        if (owner.id === data.vesselId) {
          data.vessel.owners = owner.owners;
        }
      }

      for (const prHistory of prHistories) {
        if (prHistory.id === data.id) {
          data.planningRequestHistories = prHistory.planningRequestHistories;
        }
      }
      const dataFilterAuditReport = internalAuditReports.filter(
        (internalAuditReport) => data.id === internalAuditReport.planningRequestId,
      );
      data.internalAuditReport =
        dataFilterAuditReport.length > 0 ? dataFilterAuditReport[0] : undefined;

      for (const auditor of auditors) {
        if (auditor.id === data.id) {
          data.auditors = auditor.auditors;
        }
      }
      for (const vessel of vesselDocHolders) {
        if (data.vessel && vessel.id === data.id) {
          data.vessel.vesselDocHolders = vessel.vessel?.vesselDocHolders;
        }
      }
    }

    // list inspection history

    if (query.isInspectionHistory && query.planningRequestId) {
      const pr = await this.connection
        .getCustomRepository(PlanningRequestRepository)
        .createQueryBuilder('planningRequest')
        .leftJoin('planningRequest.auditTypes', 'auditTypes')
        .where('planningRequest.id = :id', {
          id: query.planningRequestId,
        })
        .select(['planningRequest.id', 'auditTypes.id', 'auditTypes.name'])
        .getOne();
      const prAuditTypes = pr.auditTypes;

      const idsToFilter = [];
      for (const data of planningRequestData) {
        const checkAuditTypes = prAuditTypes.some((item1) =>
          data.auditTypes.some((item2) => item1.id === item2.id),
        );
        if (!checkAuditTypes) {
          idsToFilter.push(data.id);
        }
      }

      idsToFilter.push(query.planningRequestId);
      planningRequestData = planningRequestData.filter((data) => !idsToFilter.includes(data.id));
    }
    // let planningRequestResults = [...planningRequestData];
    // const roles = await this.manager.getCustomRepository(UserRepository).getUserRoles([token.id]);
    //
    // planningRequestResults = await this.filterPRByWorkflow(
    //   planningRequestData,
    //   token.id,
    //   false,
    //   null,
    //   !RoleScopeCheck.isAdmin(token) && roles.size > 0,
    // );

    return {
      data: planningRequestData,
      page: dataList.page,
      pageSize: dataList.pageSize,
      totalPage:
        query.isInspectionHistory && query.planningRequestId
          ? Math.ceil(planningRequestData.length / dataList.pageSize)
          : dataList.totalPage,
      totalItem:
        query.isInspectionHistory && query.planningRequestId
          ? planningRequestData.length
          : dataList.totalItem,
    };
  }

  async _supportWhereMainCompanyEntityTypeOffice(companyId: string) {
    const listCompany = await this.manager
      .getCustomRepository(CompanyRepository)
      .createQueryBuilder('company')
      .where('company.parentId = :companyId and company.deleted = false', {
        companyId,
      })
      .select(['company.id'])
      .getMany();
    let orWhereCondition = '1=1';
    if (listCompany.length > 0) {
      // get all user belong list company
      const listCompanyId = [];
      for (const company of listCompany) {
        orWhereCondition += ` or planningRequest.auditCompanyId = '${company.id}'`;
        listCompanyId.push(company.id);
      }
      const listUser = await this.manager
        .getCustomRepository(UserRepository)
        .createQueryBuilder('user')
        .where('user.companyId IN (:...listCompanyId) and user.deleted = false', {
          listCompanyId,
        })
        .select(['user.id'])
        .getMany();

      for (const user of listUser) {
        orWhereCondition += ` or auditors.id = '${user.id}' or user.id = '${user.id}'`;
      }
    }
    return orWhereCondition;
  }

  async _supportWhereMainCompanyEntityTypeOfficeRawQuery(
    companyId: string,
    aliasTable?: string,
    aliasAuditor?: string,
    aliasUserAssignment?: string,
  ) {
    if (!aliasTable) {
      aliasTable = 'pr';
    }
    if (!aliasAuditor) {
      aliasAuditor = 'auditors';
    }
    if (!aliasUserAssignment) {
      aliasUserAssignment = 'user';
    }
    const listCompany = await this.manager
      .getCustomRepository(CompanyRepository)
      .createQueryBuilder('company')
      .where('company.parentId = :companyId and company.deleted = false', {
        companyId,
      })
      .select(['company.id'])
      .getMany();
    let orWhereCondition = '1=1';
    if (listCompany.length > 0) {
      // get all user belong list company
      const listCompanyId = [];
      for (const company of listCompany) {
        orWhereCondition += ` or ${aliasTable}."auditCompanyId" = '${company.id}'`;
        listCompanyId.push(company.id);
      }
      const listUser = await this.manager
        .getCustomRepository(UserRepository)
        .createQueryBuilder('user')
        .where('user.companyId IN (:...listCompanyId) and user.deleted = false', {
          listCompanyId,
        })
        .select(['user.id'])
        .getMany();

      for (const user of listUser) {
        orWhereCondition += ` or "${aliasAuditor}".id = '${user.id}' or "${aliasUserAssignment}".id = '${user.id}'`;
      }
    }
    return orWhereCondition;
  }

  async getConditionForUserNotAdmin(
    user: TokenPayloadModel,
    aliasTable?: string,
    aliasTableAuditCompany?: string,
    aliasVesselDocHolder?: string,
    aliasVesselCharterer?: string,
    aliasVesselOwner?: string,
    aliasAuditor?: string,
    aliasUserAssignment?: string,
    aliasUserDivision?: string,
  ) {
    if (!aliasTable) {
      aliasTable = 'pr';
    }
    if (!aliasTableAuditCompany) {
      aliasTableAuditCompany = 'pr';
    }
    if (!aliasAuditor) {
      aliasAuditor = 'auditors';
    }
    if (!aliasUserAssignment) {
      aliasUserAssignment = 'user';
    }
    if (!aliasVesselDocHolder) {
      aliasVesselDocHolder = 'vdh';
    }
    if (!aliasVesselCharterer) {
      aliasVesselCharterer = 'vc';
    }
    if (!aliasVesselOwner) {
      aliasVesselOwner = 'vo';
    }
    if (!aliasUserDivision) {
      aliasUserDivision = 'users';
    }
    let conditionNotAdmin = '';
    const {
      whereForExternal,
      whereForMainAndInternal,
    } = await _supportWhereDOCChartererOwnerRawQuery(
      this.manager,
      user.explicitCompanyId,
      aliasTable,
      aliasVesselDocHolder,
      aliasVesselCharterer,
      aliasVesselOwner,
    );
    if (user.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
      const orWhereConditionEntityOffice = await this._supportWhereMainCompanyEntityTypeOfficeRawQuery(
        user.companyId,
        aliasTableAuditCompany,
        aliasAuditor,
        aliasUserAssignment,
      );
      conditionNotAdmin =
        ` AND ((${aliasTable}."entityType" = '${AuditEntity.VESSEL}' and ( "${aliasUserDivision}".id = '${user.id}' ` +
        whereForMainAndInternal +
        ` )) or (${aliasTable}."entityType" = '${AuditEntity.OFFICE}' and ("${aliasAuditor}".id = '${user.id}' or "${aliasUserAssignment}".id = '${user.id}' or ${aliasTableAuditCompany}."auditCompanyId" = '${user.explicitCompanyId}' or ${orWhereConditionEntityOffice})))`;
    } else if (user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
      conditionNotAdmin =
        ` AND ((${aliasTable}."entityType" = '${AuditEntity.VESSEL}' and ( "${aliasUserDivision}".id = '${user.id}' ` +
        whereForMainAndInternal +
        ` )) or (${aliasTable}."entityType" = '${AuditEntity.OFFICE}' and ("${aliasAuditor}".id = '${user.id}' or "${aliasUserAssignment}".id = '${user.id}' or ${aliasTableAuditCompany}."auditCompanyId" = '${user.explicitCompanyId}' )))`;
    } else {
      conditionNotAdmin =
        ` AND ((${aliasTable}."entityType" = '${AuditEntity.VESSEL}' and ( "${aliasUserDivision}".id = '${user.id}' ` +
        whereForExternal +
        ` )) or (${aliasTable}."entityType" = '${AuditEntity.OFFICE}' and ("${aliasAuditor}".id = '${user.id}' or "${aliasUserAssignment}".id = '${user.id}' or ${aliasTableAuditCompany}."auditCompanyId" = '${user.explicitCompanyId}' )))`;
    }
    return conditionNotAdmin;
  }

  async _listPlanningRequestNeedReview(query: ListPRNeedReviewQueryDTO, user: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('planningRequest.departments', 'departments')
      .innerJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .where(
        `planningRequest.companyId = :companyId AND 
              planningRequest.status IN (:...status) AND
              leadAuditor.id = :leadAuditorId`,
        {
          companyId: user.companyId,
          status: [
            PlanningRequestStatus.SUBMITTED,
            PlanningRequestStatus.REVIEWED_1,
            PlanningRequestStatus.REVIEWED_2,
            PlanningRequestStatus.REVIEWED_3,
            PlanningRequestStatus.REVIEWED_4,
            PlanningRequestStatus.REVIEWED_5,
          ],
          leadAuditorId: user.id,
        },
      )
      .select([
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.auditNo',
        'planningRequest.status',
        'planningRequest.timezone',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.createdAt',
        'planningRequest.entityType',
      ])
      .addSelect([
        'auditCompany.name',
        'vessel.id',
        'vessel.name',
        'vessel.code',
        'departments.id',
        'departments.name',
        'departments.code',
        'leadAuditor.id',
        'leadAuditor.username',
      ]);

    if (query.fromDate) {
      queryBuilder.andWhere(`planningRequest.createdAt >= :fromDate`, {
        fromDate: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere(`planningRequest.createdAt <= :toDate`, {
        toDate: new Date(query.toDate),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('planningRequest.entityType = :entityType', {
        entityType: query.entityType,
      });
    }

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'planningRequest',
      );
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        user,
        whereForMainAndInternal,
        whereForExternal,
        'planningRequest',
        true,
      );
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'planningRequest.createdAt:-1',
      },
    );
  }

  async listPRForCreatingReportFinding(
    query: ListPlanningRequestQueryDTO,
    user: TokenPayloadModel,
  ) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .leftJoin('planningRequest.reportFindingForm', 'reportFindingForm')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('vessel.owners', 'owners')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      .leftJoin('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .leftJoinAndSelect(
        'planningRequest.prDueDateAndDateOfLastInspections',
        'prDueDateAndDateOfLastAudits',
      )
      .leftJoin('planningRequest.company', 'company')
      .where(
        'planningRequest.vesselId = :vesselId AND auditTimeTable.status = :auditTimeTableStatus AND auditors.id = :userId AND reportFindingForm.id IS NULL',
        {
          vesselId: query.vesselId,
          auditTimeTableStatus: AuditTimeTableStatus.SUBMITTED,
          userId: user.id,
        },
      )
      .andWhere('(planningRequest.companyId = :companyId)', { companyId: user.companyId })
      .select()
      .addSelect([
        'auditTimeTable.actualFrom',
        'auditTimeTable.actualTo',
        'auditors.id',
        'auditors.username',
        'leadAuditor.id',
        'leadAuditor.username',
        'auditTypes.id',
        'auditTypes.name',
        'vessel.name',
        'vessel.isCompanyRestricted',
        'vessel.isVesselRestricted',
        'owners.username',
        'fromPort.name',
        'toPort.name',
        'userAssignments.id',
        'userAssignments.permission',
        'user.id',
        'user.username',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
      ]);

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'planningRequest',
      );
      this._buildPRQueryUser(queryBuilder, false);
      if (user.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
        const orWhereConditionEntityOffice = await this._supportWhereMainCompanyEntityTypeOffice(
          user.companyId,
        );
        // console.log(orWhereConditionEntityOffice);
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
            whereForMainAndInternal +
            ` )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId or ${orWhereConditionEntityOffice})))`,
          {
            userId: user.id,
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
            explicitCompanyId: user.explicitCompanyId,
          },
        );
      } else if (user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
            whereForMainAndInternal +
            ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))',
          {
            userId: user.id,
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
            explicitCompanyId: user.explicitCompanyId,
          },
        );
      } else {
        queryBuilder.andWhere(
          `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
            whereForExternal +
            ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))',
          {
            userId: user.id,
            entityTypeVessel: AuditEntity.VESSEL,
            entityTypeOffice: AuditEntity.OFFICE,
            explicitCompanyId: user.explicitCompanyId,
          },
        );
      }
    }
    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'planningRequest.createdAt:-1',
      },
    );
  }

  async listPRForCreatingAuditTimeTable(
    query: ListPlanningRequestQueryDTO,
    token: TokenPayloadModel,
  ) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .leftJoin('planningRequest.createdUser', 'createdUser')
      .leftJoin('planningRequest.updatedUser', 'updatedUser')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('vessel.country', 'country')
      // .leftJoin('vessel.fleet', 'fleet')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('userAssignments.user', 'user')
      .leftJoinAndSelect(
        'planningRequest.prDueDateAndDateOfLastInspections',
        'prDueDateAndDateOfLastAudits',
      )
      .leftJoin('planningRequest.company', 'company')
      .leftJoin('planningRequest.departments', 'departments')
      .where('(auditTimeTable.id IS NULL)')
      .andWhere('(planningRequest.companyId = :companyId)', {
        companyId: token.companyId,
      })
      .select()
      .addSelect([
        'createdUser.username',
        'updatedUser.username',
        'vessel.id',
        'vessel.name',
        'vessel.code',
        'vessel.imoNumber',
        // 'vessel.countryFlag',
        'vessel.countryId',
        'vessel.country',
        'vessel.isCompanyRestricted',
        'vessel.isVesselRestricted',
        'country.id',
        'country.name',
        // 'vessel.fleet',
        'vessel.vesselType',
        // 'fleet.id',
        // 'fleet.name',
        'vesselType.id',
        'vesselType.name',
        'auditTypes.id',
        'auditTypes.name',
        'auditors.id',
        'auditors.employeeId',
        'auditors.username',
        'leadAuditor.id',
        'leadAuditor.employeeId',
        'leadAuditor.username',
        'auditTimeTable.deleted',
        'userAssignments.id',
        'userAssignments.permission',
        'auditCompany.id',
        'auditCompany.name',
        'auditCompany.isCompanyRestricted',
        'user.id',
        'user.username',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
      ]);

    if (query.content) {
      queryBuilder.andWhere('(planningRequest.refId ILIKE :content)', {
        content: `%${query.content}%`,
      });
    }
    if (query.status) {
      queryBuilder.andWhere('planningRequest.status = :status', {
        status: query.status,
      });
    }
    if (query.vesselId) {
      queryBuilder.andWhere('planningRequest.vesselId = :vesselId', {
        vesselId: query.vesselId,
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('planningRequest.entityType = :entityType', {
        entityType: query.entityType,
      });
    }

    if (query.departmentId) {
      queryBuilder.andWhere('departments.id = :departmentId', {
        departmentId: query.departmentId,
      });
    }
    if (query.auditCompanyId) {
      queryBuilder.andWhere('planningRequest.auditCompanyId = :auditCompanyId', {
        auditCompanyId: query.auditCompanyId,
      });
    }
    if (!RoleScopeCheck.isAdmin(token)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        token.explicitCompanyId,
        'planningRequest',
      );
      this._buildPRQueryUser(queryBuilder, false);
      const isGetList = query.entityType && query.entityType === AuditEntity.VESSEL;
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        token,
        whereForMainAndInternal,
        whereForExternal,
        'planningRequest',
        false,
        isGetList,
      );
    }
    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'planningRequest.createdAt:-1',
      },
    );
  }

  async getDetailPRById(id: string, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .where('planningRequest.id = :id', {
        id,
      })
      .andWhere('planningRequest.deleted = :deleted AND (planningRequest.companyId = :companyId)', {
        deleted: false,
        companyId: token.companyId,
      })
      .select(['planningRequest.id', 'planningRequest.vesselId']);
    const planningRequestFound = await queryBuilder.getOne();
    if (!planningRequestFound) {
      throw new BaseError({ status: 404, message: 'planningRequest.NOT_FOUND' });
    }
    const queryBuilderPlanningRequest = this.createQueryBuilder('planningRequest')
      .where('planningRequest.id = :id', {
        id,
      })
      .andWhere('planningRequest.deleted = :deleted AND (planningRequest.companyId = :companyId)', {
        deleted: false,
        companyId: token.companyId,
      })
      // .leftJoinAndSelect('planningRequest.planningRequestHistories', 'planningRequestHistories')
      .leftJoinAndSelect('planningRequest.auditTypes', 'auditTypes')
      // .leftJoin('auditTypes.inspectionMapping', 'inspectionMapping')
      // .leftJoin('inspectionMapping.selfAssessments', 'selfAssessments')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      .leftJoinAndSelect(
        'planningRequest.prDueDateAndDateOfLastInspections',
        'prDueDateAndDateOfLastAudits',
      )
      .leftJoin('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('planningRequest.location', 'location')
      .leftJoin('planningRequest.company', 'company')
      // .leftJoinAndSelect('vessel.fleet', 'fleet')
      .leftJoinAndSelect('vessel.vesselType', 'vesselType')
      .leftJoin('vessel.owners', 'owners')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('auditors.company', 'auditorCompany')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      // .leftJoinAndSelect('planningRequest.planningRequestOfficeComments', 'officeComments')
      // .leftJoinAndSelect(
      //   'planningRequest.pRFocusRequests',
      //   'pRFocusRequests',
      //   `("pRFocusRequests"."focusRequestObj" ->> 'status' = :status)`,
      //   {
      //     status: CommonStatus.ACTIVE,
      //   },
      // )
      // .leftJoin('pRFocusRequests.focusRequest', 'focusRequest')
      .leftJoinAndSelect(
        'planningRequest.planningRequestAdditionalReviewers',
        'additionalReviewers',
      )
      .leftJoin('planningRequest.createdUser', 'createdUser')
      .leftJoin('planningRequest.updatedUser', 'updatedUser')
      .leftJoin('additionalReviewers.createdUser', 'additionalReviewersCreatedUser')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.voyageType', 'voyageType')
      //.leftJoin('planningRequest.department', 'department')
      .leftJoin('planningRequest.departments', 'departments')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('user.company', 'userCompany')
      .leftJoin('user.divisions', 'userDivisions')
      .select()
      // .leftJoin(
      //   'vessel.vesselDocHolders',
      //   'vesselDocHolders',
      //   // 'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now()',
      // )
      // // .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      // .addSelect([
      //   'vesselDocHolders.id',
      //   'vesselDocHolders.companyId',
      //   'vesselDocHolders.fromDate',
      //   'vesselDocHolders.toDate',
      //   'vesselDocHolders.responsiblePartyInspection',
      //   'vesselDocHolders.responsiblePartyQA',
      //   'vesselDocHolders.status',
      //   // 'companyVesselDocHolders.name',
      // ])
      // .leftJoin(
      //   'vessel.vesselOwners',
      //   'vesselOwners',
      //   // 'vesselOwners.fromDate < now() and COALESCE(vesselOwners.toDate,now()) >= now()',
      // )
      // // .leftJoin('vesselOwners.company', 'companyVesselOwnersPlans')
      // .addSelect([
      //   'vesselOwners.id',
      //   'vesselOwners.companyId',
      //   'vesselOwners.fromDate',
      //   'vesselOwners.toDate',
      //   'vesselOwners.responsiblePartyInspection',
      //   'vesselOwners.responsiblePartyQA',
      //   'vesselOwners.status',
      //   // 'companyVesselOwnersPlans.name',
      // ])
      // .leftJoin(
      //   'vessel.vesselCharterers',
      //   'vesselCharterers',
      //   // 'vesselCharterers.fromDate < now() and COALESCE(vesselCharterers.toDate,now()) >= now()',
      // )
      // // .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
      // .addSelect([
      //   'vesselCharterers.id',
      //   'vesselCharterers.companyId',
      //   'vesselCharterers.fromDate',
      //   'vesselCharterers.toDate',
      //   'vesselCharterers.responsiblePartyInspection',
      //   'vesselCharterers.responsiblePartyQA',
      //   'vesselCharterers.status',
      //   // 'companyVesselCharterers.name',
      // ])
      .addSelect([
        'userAssignments.id',
        'userAssignments.userId',
        'userAssignments.permission',
        'userAssignments.isEdit',
        'user.id',
        'user.username',
        'user.jobTitle',
        'user.roles',
        'userCompany.name',
        'userCompany.code',
        'company.name',
        'userDivisions.name',
        'userDivisions.code',
        'createdUser.username',
        'updatedUser.username',
        'additionalReviewersCreatedUser.username',
        'additionalReviewersCreatedUser.id',
        'owners.id',
        'owners.username',
        'auditors.id',
        'auditors.username',
        'auditors.email',
        'auditorCompany.id',
        'auditorCompany.email',
        'auditCompany.isCompanyRestricted',
        'leadAuditor.id',
        'leadAuditor.username',
        'leadAuditor.email',
        'auditCompany.name',
        'auditCompany.isCompanyRestricted',
        'vessel.id',
        'vessel.name',
        'vessel.code',
        'vessel.imoNumber',
        'vessel.fleetName',
        'vessel.isCompanyRestricted',
        'vessel.isVesselRestricted',
        'fromPort.id',
        'fromPort.name',
        'toPort.id',
        'toPort.name',
        'departments.id',
        'departments.name',
        'location.id',
        'location.name',
        'voyageType.id',
        'voyageType.name',
        // 'selfAssessments.standardMasterId',
        // 'selfAssessments.auditCompanyId',
        // 'inspectionMapping.isSA'
      ]);
    // .orderBy('"focusRequest"."createdAt"', 'DESC');
    const queryPRFocusRequests = this.manager
      .getCustomRepository(PlanningRequestFocusRequestRepository)
      .createQueryBuilder('pRFocusRequest')
      .leftJoin('pRFocusRequest.focusRequest', 'focusRequest')
      .where(`(pRFocusRequest.planningRequestId = :id)`, {
        id,
        status: CommonStatus.ACTIVE,
      })
      .orderBy('"focusRequest"."createdAt"', 'DESC');

    const queryPlanningRequestHistories = this.manager
      .getCustomRepository(PlanningRequestHistoryRepository)
      .createQueryBuilder('PlanningRequestHistory')
      .where(`(PlanningRequestHistory.planningRequestId = :id)`, {
        id,
      })
      .orderBy('"PlanningRequestHistory"."createdAt"', 'DESC');

    const queryPlanningRequestOfficeComments = this.manager
      .getCustomRepository(PlanningRequestOfficeCommentRepository)
      .createQueryBuilder('PROfficeComment')
      .where(`(PROfficeComment.planningRequestId = :id)`, {
        id,
      })
      .orderBy('"PROfficeComment"."createdAt"', 'DESC');

    // const planningRequest = await queryBuilder.getOne();

    const [
      planningRequest,
      pRFocusRequests,
      planningRequestHistories,
      planningRequestOfficeComments,
    ] = await Promise.all([
      queryBuilderPlanningRequest.getOne(),
      queryPRFocusRequests.getMany(),
      queryPlanningRequestHistories.getMany(),
      queryPlanningRequestOfficeComments.getMany(),
    ]);
    if (planningRequestFound?.vesselId) {
      let whereVesselDocHolder =
        '(VesselDocHolder.companyId = :companyId and deleted = false and VesselDocHolder.vesselId = :vesselId)';
      if (token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
        whereVesselDocHolder =
          '(VesselDocHolder.companyId = :companyId and deleted = false and VesselDocHolder.vesselId = :vesselId and VesselDocHolder.responsiblePartyInspection = true)';
      }
      let whereVesselCharterer =
        '(VesselCharterer.companyId = :companyId and deleted = false and VesselCharterer.vesselId = :vesselId)';
      if (token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
        whereVesselCharterer =
          '(VesselCharterer.companyId = :companyId and deleted = false and VesselCharterer.vesselId = :vesselId and VesselCharterer.responsiblePartyInspection = true)';
      }
      let whereVesselOwner =
        '(VesselOwner.companyId = :companyId and deleted = false and VesselOwner.vesselId = :vesselId)';
      if (token.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY) {
        whereVesselOwner =
          '(VesselOwner.companyId = :companyId and deleted = false and VesselOwner.vesselId = :vesselId and VesselOwner.responsiblePartyInspection = true)';
      }
      const queryVesselDocHolders = this.manager
        .getCustomRepository(VesselDocHolderRepository)
        .createQueryBuilder('VesselDocHolder')
        .where(whereVesselDocHolder, {
          companyId: token.explicitCompanyId,
          vesselId: planningRequestFound.vesselId,
        });
      const queryVesselCharterers = this.manager
        .getCustomRepository(VesselChartererRepository)
        .createQueryBuilder('VesselCharterer')
        .where(whereVesselCharterer, {
          companyId: token.explicitCompanyId,
          vesselId: planningRequestFound.vesselId,
        });
      const queryVesselOwners = this.manager
        .getCustomRepository(VesselOwnerRepository)
        .createQueryBuilder('VesselOwner')
        .where(whereVesselOwner, {
          companyId: token.explicitCompanyId,
          vesselId: planningRequestFound.vesselId,
        });
      const [vesselDocHolders, vesselCharterers, vesselOwners] = await Promise.all([
        queryVesselDocHolders.getMany(),
        queryVesselCharterers.getMany(),
        queryVesselOwners.getMany(),
      ]);
      Object.assign(planningRequest, { vesselDocHolders, vesselCharterers, vesselOwners });
    }
    // if(!planningRequest?.isSA){
    //   planningRequest?.auditTypes?.forEach(item => {
    //     delete item.inspectionMapping;
    //   });
    // }
    Object.assign(planningRequest, {
      pRFocusRequests,
      planningRequestHistories,
      planningRequestOfficeComments,
    });

    const userIds = [];
    planningRequest?.userAssignments?.map((userAssignment) => {
      if (!userIds.includes(userAssignment.user.id)) {
        userIds.push(userAssignment.user.id);
      }
    });

    let mapRoles;
    const mapUsers = new Map<string, User>();
    if (userIds.length > 0) {
      mapRoles = await this.connection.getCustomRepository(UserRepository).getUserRoles(userIds);
    }

    const uAF = [
      {
        creator: [],
        reviewer1: [],
        reviewer2: [],
        reviewer3: [],
        reviewer4: [],
        reviewer5: [],
        approver: [],
      },
    ];
    const Pstatus = [
      PlanningRequestStatus.DRAFT,
      PlanningRequestStatus.APPROVED,
      PlanningRequestStatus.CANCELLED,
    ];
    // direct feature code starts here
    if (!Pstatus.includes(planningRequest?.status as PlanningRequestStatus)) {
      const sql = `SELECT workflow.*, workflow_role.* FROM workflow LEFT JOIN workflow_role ON workflow.id = "workflow_role"."workflowId" WHERE "companyId" =$1 and "workflowType" = $2 and status = 'Published'`;
      const values = [token.companyId, 'Planning request'];
      const workFlows = await this.connection.query(sql, values);
      const directApproval = await this._checkIsDirectFeature(
        planningRequest?.userAssignments,
        workFlows,
        token,
        planningRequest?.status,
        planningRequest?.previousStatus,
        planningRequest,
      );
      if (directApproval.isDirectApproved) {
        planningRequest['currentReviewerStep'] = directApproval.currentReviewerStep;
        planningRequest['isDirectApproved'] = directApproval.isDirectApproved;
      } else {
        planningRequest['isDirectApproved'] = directApproval.isDirectApproved;
      }
      if (Object.keys(directApproval)?.includes('isReviewEnabled')) {
        planningRequest['currentReviewerStep'] = directApproval?.currentReviewerStep;
        planningRequest['isReviewEnabled'] = directApproval?.isReviewEnabled;
      }
    }
    // direct feature code ends here
    planningRequest?.userAssignments?.forEach((userAssignment) => {
      if (mapRoles.has(userAssignment.user.id)) {
        userAssignment.user.roles = mapRoles.get(userAssignment.user.id);
      }
      uAF[0][userAssignment.permission]?.push(userAssignment);
      if (!mapUsers.has(userAssignment.userId)) {
        mapUsers.set(userAssignment.userId, userAssignment.user);
      }
    });
    planningRequest?.planningRequestHistories?.forEach((item) => {
      if (mapUsers.has(item.createdUser.id)) {
        Object.assign(item.createdUser, {
          divisions: mapUsers.get(item.createdUser.id).divisions,
          company: mapUsers.get(item.createdUser.id).company,
          roles: mapUsers.get(item.createdUser.id).roles,
        });
      }
    });
    planningRequest['userAssignmentsFlow'] = uAF;
    const result = await this.filterPRByWorkflow([planningRequest], token.id, false, null, false);
    return result.length ? result[0] : planningRequest;
  }

  private _buildPRQueryUser(queryBuilder, getVesselDocHolders: boolean) {
    queryBuilder
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      // .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
      // .leftJoin('vessel.vesselOwners', 'vesselOwners')
      .leftJoin('divisionMapping.division', 'division')
      .leftJoin('division.users', 'users');
    if (getVesselDocHolders) {
      queryBuilder
        .leftJoin(
          'vessel.vesselDocHolders',
          'vesselDocHolders',
          // 'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now()',
        )
        .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .addSelect([
          'vesselDocHolders.id',
          'vesselDocHolders.companyId',
          'vesselDocHolders.fromDate',
          'vesselDocHolders.toDate',
          'vesselDocHolders.responsiblePartyInspection',
          'vesselDocHolders.responsiblePartyQA',
          'vesselDocHolders.status',
          'companyVesselDocHolders.name',
        ]);
    }
    queryBuilder
      .leftJoin(
        'vessel.vesselOwners',
        'vesselOwners',
        // 'vesselOwners.fromDate < now() and COALESCE(vesselOwners.toDate,now()) >= now()',
      )
      .leftJoin('vesselOwners.company', 'companyVesselOwnersPlans')
      .addSelect([
        'vesselOwners.id',
        'vesselOwners.companyId',
        'vesselOwners.fromDate',
        'vesselOwners.toDate',
        'vesselOwners.responsiblePartyInspection',
        'vesselOwners.responsiblePartyQA',
        'vesselOwners.status',
        'companyVesselOwnersPlans.name',
      ])
      .leftJoin(
        'vessel.vesselCharterers',
        'vesselCharterers',
        // 'vesselCharterers.fromDate < now() and COALESCE(vesselCharterers.toDate,now()) >= now()',
      )
      .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
      .addSelect([
        'vesselCharterers.id',
        'vesselCharterers.companyId',
        'vesselCharterers.fromDate',
        'vesselCharterers.toDate',
        'vesselCharterers.responsiblePartyInspection',
        'vesselCharterers.responsiblePartyQA',
        'vesselCharterers.status',
        'companyVesselCharterers.name',
      ]);
  }

  async listPRForGranttChart(user: TokenPayloadModel, query: ListPRGraphicallyDTO) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.company', 'company')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('planningRequest.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .where('(planningRequest.companyId = :companyId)', {
        companyId: user.companyId,
      })
      .select([
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.auditNo',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.status',
        'planningRequest.entityType',
        'leadAuditor.id',
        'leadAuditor.username',
        'company.id',
        'company.name',
        'vessel.id',
        'vessel.name',
      ])
      .addSelect([
        'auditors.id',
        'auditors.username',
        'auditors.companyId',
        'userAssignments.id',
        'userAssignments.userId',
        'userAssignments.permission',
      ]);

    if (query.status) {
      queryBuilder.andWhere('planningRequest.status = :status', {
        status: query.status,
      });
    }

    if (query.excludeStatus) {
      const excludeStatusFilter = query.excludeStatus.split(',');

      const indexReviewed = excludeStatusFilter.indexOf(PRStatusFilter.REVIEWED);
      if (indexReviewed >= 0) {
        excludeStatusFilter.splice(indexReviewed, 1);
        excludeStatusFilter.push(
          PlanningRequestStatus.REVIEWED_1,
          PlanningRequestStatus.REVIEWED_2,
          PlanningRequestStatus.REVIEWED_3,
          PlanningRequestStatus.REVIEWED_4,
          PlanningRequestStatus.REVIEWED_5,
        );
      }
      queryBuilder.andWhere('planningRequest.status NOT IN (:...status)', {
        status: excludeStatusFilter,
      });
    }

    // IMPORTANT: Search PR overlaps with given date range
    if (query.fromDate) {
      queryBuilder.andWhere('planningRequest.plannedToDate >= :fromDateParam', {
        fromDateParam: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere('planningRequest.plannedFromDate <= :toDateParam', {
        toDateParam: new Date(query.toDate),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('planningRequest.entityType = :entityType', {
        entityType: query.entityType,
      });
    }

    if (query.auditorName) {
      queryBuilder.andWhere('auditors.username ILIKE :auditorName', {
        auditorName: `%${query.auditorName}%`,
      });
    }

    if (query.groupBy) {
      switch (query.groupBy) {
        // case GraphicalGroupByEnum.AUDITOR:
        //   queryBuilder
        //     .leftJoin('planningRequest.auditors', 'auditors')
        //     .addSelect(['auditors.id', 'auditors.username']);
        //   break;
        case GraphicalGroupByEnum.VESSEL:
          queryBuilder
            .andWhere('planningRequest.entityType = :auditEntity', {
              auditEntity: AuditEntity.VESSEL,
            })
            // .andWhere('planningRequest.vesselId IS NOT NULL')
            .addSelect(['vessel.id', 'vessel.name']);
          if (query.vesselName) {
            queryBuilder.andWhere('vessel.name ILIKE :vesselName', {
              vesselName: `%${query.vesselName}%`,
            });
          }
          break;
        case GraphicalGroupByEnum.AUDITOR:
          queryBuilder
            .leftJoin('planningRequest.departments', 'departments')
            .leftJoin('planningRequest.auditCompany', 'auditCompany')
            .addSelect([
              'departments.id',
              'departments.companyId',
              'departments.name',
              'auditCompany.id',
              'auditCompany.name',
            ]);
          if (query.companyName) {
            queryBuilder.andWhere('auditCompany.name ILIKE :companyName', {
              companyName: `%${query.companyName}%`,
            });
          }
          break;
        case GraphicalGroupByEnum.OFFICE:
          queryBuilder
            .andWhere('planningRequest.entityType = :auditEntity', {
              auditEntity: AuditEntity.OFFICE,
            })
            //.leftJoin('planningRequest.department', 'department')
            .leftJoin('planningRequest.departments', 'departments')
            .leftJoin('planningRequest.auditCompany', 'auditCompany')
            .addSelect([
              'departments.id',
              'departments.companyId',
              'departments.name',
              'auditCompany.id',
              'auditCompany.name',
            ]);
          if (query.companyName) {
            queryBuilder.andWhere('auditCompany.name ILIKE :companyName', {
              companyName: `%${query.companyName}%`,
            });
          }
      }
    }

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'planningRequest',
      );
      this._buildPRQueryUser(queryBuilder, true);
      if (
        user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
        (await this.checkRoleAndCompanyType(user))
      ) {
        if (
          !query.entityType &&
          (!query.groupBy || query.groupBy === GraphicalGroupByEnum.AUDITOR)
        ) {
          queryBuilder.andWhere(
            `((planningRequest.entityType = :entityTypeVessel and (auditors.id = :userId or user.id = :userId)) 
            or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))`,
            {
              userId: user.id,
              entityTypeVessel: AuditEntity.VESSEL,
              entityTypeOffice: AuditEntity.OFFICE,
              explicitCompanyId: user.explicitCompanyId,
            },
          );
        }
        if (
          (query.entityType && query.entityType === AuditEntity.VESSEL) ||
          (query.groupBy && query.groupBy === GraphicalGroupByEnum.VESSEL)
        ) {
          queryBuilder.andWhere(
            `(planningRequest.entityType = :entityTypeVessel and auditors.id = :userId)`,
            {
              userId: user.id,
              entityTypeVessel: AuditEntity.VESSEL,
            },
          );
        }
      } else {
        if (
          (query.entityType && query.entityType === AuditEntity.VESSEL) ||
          (query.groupBy && query.groupBy === GraphicalGroupByEnum.VESSEL)
        ) {
          if (
            user.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
            user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
          ) {
            queryBuilder.andWhere(`( users.id = :userId ` + whereForMainAndInternal + ')', {
              userId: user.id,
            });
          } else {
            queryBuilder.andWhere(`( users.id = :userId ` + whereForExternal + ')', {
              userId: user.id,
            });
          }
        }

        if (
          !query.entityType &&
          (!query.groupBy || query.groupBy === GraphicalGroupByEnum.AUDITOR)
        ) {
          if (user.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
            queryBuilder.andWhere(
              `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
                whereForMainAndInternal +
                ' )) or (planningRequest.entityType = :entityTypeOffice))',
              {
                userId: user.id,
                entityTypeVessel: AuditEntity.VESSEL,
                entityTypeOffice: AuditEntity.OFFICE,
              },
            );
          } else if (user.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
            queryBuilder.andWhere(
              `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
                whereForMainAndInternal +
                ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))',
              {
                userId: user.id,
                entityTypeVessel: AuditEntity.VESSEL,
                entityTypeOffice: AuditEntity.OFFICE,
                explicitCompanyId: user.explicitCompanyId,
              },
            );
          } else {
            queryBuilder.andWhere(
              `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
                whereForExternal +
                ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))',
              {
                userId: user.id,
                entityTypeVessel: AuditEntity.VESSEL,
                entityTypeOffice: AuditEntity.OFFICE,
                explicitCompanyId: user.explicitCompanyId,
              },
            );
          }
        }
      }
    }

    return this.list(
      {
        page: query.page || 1,
        limit: query.pageSize || 10000,
      },
      {
        queryBuilder,
        sort: query.sort || 'planningRequest.plannedFromDate:1',
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );
  }

  async getDetailInGeneral(id: string, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .where(
        `planningRequest.id = :id AND 
        planningRequest.deleted = :deleted AND 
        (planningRequest.companyId = :companyId)`,
        {
          id,
          deleted: false,
          companyId: token.companyId,
        },
      )
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      // .leftJoin('planningRequest.company', 'company')
      .leftJoin('planningRequest.vessel', 'vessel')
      // .leftJoin('vessel.fleet', 'fleet')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoinAndSelect(
        'planningRequest.prDueDateAndDateOfLastInspections',
        'prDueDateAndDateOfLastAudits',
      )
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      //.leftJoin('planningRequest.department', 'department')
      .leftJoin('planningRequest.departments', 'departments')
      .select([
        'planningRequest.id',
        'planningRequest.refId',
        'planningRequest.auditNo',
        'planningRequest.status',
        'planningRequest.refId',
        'planningRequest.workingType',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.entityType',
        'planningRequest.typeOfAudit',
        'planningRequest.isSA',
      ])
      .addSelect([
        'auditTypes.id',
        'auditTypes.name',
        'vessel.id',
        'vessel.name',
        'vessel.isCompanyRestricted',
        'vessel.isVesselRestricted',
        // 'fleet.name',
        'vesselType.name',
        'auditors.id',
        'auditors.username',
        'leadAuditor.id',
        'leadAuditor.username',
        'auditCompany.id',
        'auditCompany.name',
        'auditCompany.isCompanyRestricted',
        'departments.id',
        'departments.name',
      ])
      .leftJoin(
        'vessel.vesselDocHolders',
        'vesselDocHolders',
        // 'vesselDocHolders.fromDate < now() and COALESCE(vesselDocHolders.toDate,now()) >= now()',
      )
      .leftJoin('vessel.divisionMapping', 'divisionMapping')
      .leftJoin('divisionMapping.division', 'division')
      .addSelect(['divisionMapping.id', 'division.id', 'division.code', 'division.name'])
      // .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .addSelect([
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        // 'companyVesselDocHolders.name',
      ])
      .leftJoin(
        'vessel.vesselOwners',
        'vesselOwners',
        // 'vesselOwners.fromDate < now() and COALESCE(vesselOwners.toDate,now()) >= now()',
      )
      // .leftJoin('vesselOwners.company', 'companyVesselOwnersPlans')
      .addSelect([
        'vesselOwners.id',
        'vesselOwners.companyId',
        'vesselOwners.fromDate',
        'vesselOwners.toDate',
        'vesselOwners.responsiblePartyInspection',
        'vesselOwners.responsiblePartyQA',
        'vesselOwners.status',
        // 'companyVesselOwnersPlans.name',
      ])
      .leftJoin(
        'vessel.vesselCharterers',
        'vesselCharterers',
        // 'vesselCharterers.fromDate < now() and COALESCE(vesselCharterers.toDate,now()) >= now()',
      )
      // .leftJoin('vesselCharterers.company', 'companyVesselCharterers')
      .addSelect([
        'vesselCharterers.id',
        'vesselCharterers.companyId',
        'vesselCharterers.fromDate',
        'vesselCharterers.toDate',
        'vesselCharterers.responsiblePartyInspection',
        'vesselCharterers.responsiblePartyQA',
        'vesselCharterers.status',
        // 'companyVesselCharterers.name',
      ]);

    const planningRequest = await queryBuilder.getOne();

    if (planningRequest) {
      return planningRequest;
    } else {
      throw new BaseError({ status: 404, message: 'planningRequest.NOT_FOUND' });
    }
  }

  async listAuditCheckListsByPR(planningId: string) {
    return this.manager
      .getCustomRepository(AuditChecklistRepository)
      ._listValidAuditCheckListsByPR(planningId, null, []);
  }

  async updatePR(
    planningRequestId: string,
    params: UpdatePlanningRequestDTO,
    preparedPRParams: PlanningRequest,
    // preparedPRAuditors: PRAuditor[],
    PROfficeCommentParams: PROfficeComment[],
    focusRequestsParams: PRFocusRequest[],
    // preparedPRAdditionalReviewers: PRAdditionalReviewer[],
    token: TokenPayloadModel,
    query: SubmitPRQueryDTO,
    dueDateAndDateOfLastInspections?: DueDateAndDateOfLastInspectionDTO[],
  ) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataMail: IEmailEventModel[] = [];
      const PRFound = await this.detailByConditions(
        {
          id: planningRequestId,
          companyId: token.companyId,
          status: In([
            PlanningRequestStatus.APPROVED,
            PlanningRequestStatus.AUDITOR_ACCEPTED,
            PlanningRequestStatus.DRAFT,
            PlanningRequestStatus.SUBMITTED,
            PlanningRequestStatus.REJECTED,
            PlanningRequestStatus.REVIEWED_1,
            PlanningRequestStatus.REVIEWED_2,
            PlanningRequestStatus.REVIEWED_3,
            PlanningRequestStatus.REVIEWED_4,
            PlanningRequestStatus.REVIEWED_5,
          ]),
        },
        [
          'planningRequestOfficeComments',
          'planningRequestAdditionalReviewers',
          'auditors',
          'userAssignments',
          'pRFocusRequests',
        ],
      );
      if (!PRFound) {
        throw new BaseError({ message: 'planningRequest.CANNOT_UPDATE' });
      }
      if (preparedPRParams.vesselId) {
        const vesselDetail = await this.manager
          .getCustomRepository(VesselRepository)
          .getDetailVesselById('', preparedPRParams.vesselId);
        if (vesselDetail) {
          if (vesselDetail.vesselDocHolders.length > 0) {
            Object.assign(preparedPRParams, {
              docHolderCurrentCompanyId: vesselDetail.vesselDocHolders[0].companyId,
            });
          } else {
            Object.assign(preparedPRParams, {
              docHolderCurrentCompanyId: null,
            });
          }

          if (vesselDetail.vesselCharterers.length > 0) {
            Object.assign(preparedPRParams, {
              vesselChartererCurrentCompanyId: vesselDetail.vesselCharterers[0].companyId,
            });
          } else {
            Object.assign(preparedPRParams, {
              vesselChartererCurrentCompanyId: null,
            });
          }

          if (vesselDetail.vesselOwners.length > 0) {
            Object.assign(preparedPRParams, {
              vesselOwnerCurrentCompanyId: vesselDetail.vesselOwners[0].companyId,
            });
          } else {
            Object.assign(preparedPRParams, {
              vesselOwnerCurrentCompanyId: null,
            });
          }
        }
      }
      const monthList = Object.keys(SelfAssessmentMonthEnum).map(
        (key) => SelfAssessmentMonthEnum[key],
      );
      // const submittedDate = new Date();
      const plannedFromDate_Month = preparedPRParams.plannedFromDate.getMonth();
      const finalPlannedFromDateMonth = monthList[plannedFromDate_Month];
      const plannedFromDateYear = preparedPRParams.plannedFromDate.getFullYear();
      const plannedToDate_Month = preparedPRParams.plannedToDate.getMonth();
      const finalPlannedToDateMonth = monthList[plannedToDate_Month];
      const plannedToDateYear = preparedPRParams.plannedToDate.getFullYear();
      Object.assign(preparedPRParams, {
        plannedFromDate_Month: finalPlannedFromDateMonth,
        plannedFromDate_Year: plannedFromDateYear,
        plannedToDate_Month: finalPlannedToDateMonth,
        plannedToDate_Year: plannedToDateYear,
      });
      // check PR is Rejected --> status = DRAFT
      let paramsUpdatePR = preparedPRParams;
      if (PRFound.status == PlanningRequestStatus.REJECTED && !query.isSubmitted) {
        paramsUpdatePR = Object.assign(paramsUpdatePR, {
          status: PRFound.status,
          previousStatus: PRFound.previousStatus,
        });
      }

      //#region Office Comment
      const listCurrentPROfficeComments = PRFound.planningRequestOfficeComments;
      const listCurrentPROfficeCommentIds: string[] = [];
      const listNewPROfficeCommentIds: string[] = [];

      if (listCurrentPROfficeComments && listCurrentPROfficeComments.length > 0) {
        listCurrentPROfficeComments.forEach((item: PROfficeComment) => {
          listCurrentPROfficeCommentIds.push(item.id);
        });
      }

      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(token.id);

      // prepare PR office commment user created
      const preparedPROfficeCommentParams: PROfficeComment[] = [];
      for (let i = 0; i < PROfficeCommentParams.length; i++) {
        listNewPROfficeCommentIds.push(PROfficeCommentParams[i].id);
        preparedPROfficeCommentParams.push({
          ...PROfficeCommentParams[i],
          createdUser: PROfficeCommentParams[i].createdUser
            ? PROfficeCommentParams[i].createdUser
            : createdUser,
          updatedUser: PROfficeCommentParams[i].updatedUser
            ? PROfficeCommentParams[i].updatedUser
            : createdUser,
        });
      }

      if (PROfficeCommentParams && PROfficeCommentParams.length > 0) {
        PROfficeCommentParams.forEach((item: PROfficeComment) => {
          listNewPROfficeCommentIds.push(item.id);
        });
      }

      const listPROfficeCommentUpdateIds = MySet.intersect(
        new Set(listCurrentPROfficeCommentIds),
        new Set(listNewPROfficeCommentIds),
      );

      const listPROfficeCommentCreateIds = MySet.difference(
        new Set(listNewPROfficeCommentIds),
        new Set(listCurrentPROfficeCommentIds),
      );

      const listPROfficeCommentDeleteIds = MySet.difference(
        new Set(listCurrentPROfficeCommentIds),
        new Set(listNewPROfficeCommentIds),
      );

      const PROfficeCommentCreate = preparedPROfficeCommentParams.filter((item) =>
        listPROfficeCommentCreateIds.has(item.id),
      );

      const PROfficeCommentUpdate = preparedPROfficeCommentParams.filter((item) =>
        listPROfficeCommentUpdateIds.has(item.id),
      );
      //#endregion Office Comment
      //#region Update data
      return await this.connection.transaction(async (manager) => {
        const auditLogActivities: AuditActivityEnum[] = [];

        // check if submit PR --> status = SUBMITTED
        if (query.isSubmitted && query.isSubmitted.toString() === 'true') {
          paramsUpdatePR = Object.assign(paramsUpdatePR, {
            status: PlanningRequestStatus.SUBMITTED,
            previousStatus: PlanningRequestStatus.DRAFT,
          });

          // Create history
          await manager.insert(PlanningRequestHistory, {
            planningRequestId,
            remark: null,
            status: PlanningRequestStatus.SUBMITTED,
            createdUser: createdUser,
          });

          //#region Handle push noti to Creator, Approver, Acceptor, Owner/Manager
          const updateEditIs = [];
          if (params.userAssignment) {
            for (const value of params.userAssignment.usersPermissions) {
              if (value['permission'] === 'creator' || value['permission'] === 'reviewer1') {
                value['isView'] = true;
                updateEditIs.push(...value.userIds);
              }
            }
            // Update user assignment
            await manager
              .getCustomRepository(UserAssignmentRepository)
              .updateUserAssignment(
                manager,
                ModuleType.PLANNING_REQUEST,
                PRFound.id,
                params.userAssignment.usersPermissions,
              );
          }

          if (updateEditIs.length) {
            await manager
              .getCustomRepository(UserAssignmentRepository)
              ._acceptEditPermission(updateEditIs, planningRequestId, [
                WorkflowPermission.REVIEWER1,
                WorkflowPermission.CREATOR,
              ]);
          }

          const listUserAssignment = await manager
            .getCustomRepository(UserAssignmentRepository)
            .listByModule(ModuleType.PLANNING_REQUEST, planningRequestId);

          let listReceiverNotiAndMail = [];
          listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
            listUserAssignment[WorkflowPermission.REVIEWER1],
          );
          listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
            listUserAssignment[WorkflowPermission.APPROVER],
          );
          console.log('Draft ---> Submit: ', listReceiverNotiAndMail);

          // listReceiverNoti = listReceiverNoti.concat(
          //   listUserAssignment[WorkflowPermission.AUDITOR],
          // );
          // listReceiverNoti = listReceiverNoti.concat(
          //   listUserAssignment[WorkflowPermission.OWNER_MANAGER],
          // );

          const performer = await manager
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle']);
          dataNoti.push({
            receivers: listReceiverNotiAndMail,
            module: ModuleType.PLANNING_REQUEST,
            recordId: planningRequestId,
            recordRef: PRFound.refId,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            currentStatus: PlanningRequestStatus.SUBMITTED,
            previousStatus: PlanningRequestStatus.DRAFT,
            performer: performer,
            executedAt: new Date(),
          });

          for (const receiver of listReceiverNotiAndMail) {
            dataMail.push({
              receiver: {
                email: receiver.email,
              },
              type: EmailTypeEnum.CHANGE_RECORD_STATUS,
              templateKey: MailTemplate.CHANGE_RECORD_STATUS,
              subject: '[Notification] Change status in a record',
              data: {
                username: receiver.username,
                baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                recordRef: PRFound.refId,
                recordId: planningRequestId,
                path: ModulePathEnum.PLANNING_AND_REQUEST,
                currentStatus: PlanningRequestStatus.SUBMITTED,
                previousStatus: PlanningRequestStatus.DRAFT,
              },
            });
          }

          //#endregion Push noti

          auditLogActivities.push(AuditActivityEnum.SUBMITTED);
        } else if (
          PRFound.status == PlanningRequestStatus.REJECTED &&
          PRFound.reassignFrom === PlanningRequestStatus.REVIEWED_1
        ) {
          const isSubmiterSave = PRFound.userAssignments.filter(
            (userAssignment) =>
              userAssignment.userId === token.id &&
              userAssignment.permission == WorkflowPermission.CREATOR,
          );
          if (isSubmiterSave.length) {
            await manager.insert(PlanningRequestHistory, {
              planningRequestId,
              remark: null,
              status: PlanningRequestStatus.DRAFT,
              createdUser: createdUser,
            });
            paramsUpdatePR = Object.assign(paramsUpdatePR, {
              status: PlanningRequestStatus.DRAFT,
              previousStatus: PlanningRequestStatus.DRAFT,
            });
          }
        } else {
          auditLogActivities.push(AuditActivityEnum.UPDATED_INFO);
        }

        // Update planning request
        await manager.save(PlanningRequest, paramsUpdatePR);

        // update Due Date And Date Of Last Audits
        if (!dueDateAndDateOfLastInspections[0]?.id || !dueDateAndDateOfLastInspections.length) {
          const dueDateDeletes = await manager.find(PRDueDateAndDateOfLastInspection, {
            where: { deleted: false, planningRequestId: planningRequestId },
            select: ['id'],
          });

          const dueDateIdDeletes = dueDateDeletes?.map((item) => item.id);
          if (dueDateIdDeletes?.length) {
            await manager.delete(PRDueDateAndDateOfLastInspection, {
              id: In(Array.from(dueDateIdDeletes)),
            });
          }

          const newDueDateAndDateOfLastInspections = dueDateAndDateOfLastInspections.map(
            (dueDateAndDateOfLastAudit) => ({
              ...dueDateAndDateOfLastAudit,
              planningRequestId,
            }),
          );

          if (!dueDateAndDateOfLastInspections[0]?.id) {
            await manager.save(
              PRDueDateAndDateOfLastInspection,
              newDueDateAndDateOfLastInspections,
            );
          }
        }

        // Create new Auditor, Comment, Reviewer
        await manager.save(PROfficeComment, PROfficeCommentCreate);
        // Update existed Auditor, Comment, Reviewer
        await manager.save(PROfficeComment, PROfficeCommentUpdate);
        // Delete old
        await manager.delete(PROfficeComment, { id: In(Array.from(listPROfficeCommentDeleteIds)) });

        //Focus request
        const focusRequestMap = new Map<string, UpdatePRFocusRequestDTO>();
        focusRequestsParams?.forEach((prFocus) => focusRequestMap.set(prFocus.id, prFocus));
        const deletedFocus = PRFound.pRFocusRequests?.filter(
          (focusRequest) => !focusRequestMap.has(focusRequest.id),
        );
        if (deletedFocus.length) {
          await manager.delete(PRFocusRequest, { id: In(deletedFocus.map((it) => it.id)) });
        }

        //update PRFocusRequest
        const focusRequests = focusRequestsParams.map((focusRequest) => ({
          ...focusRequest,
          planningRequestId,
        }));
        await manager.save(PRFocusRequest, focusRequests);

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: planningRequestId,
          },
          token,
          auditLogActivities,
          createdUser,
        );
        return { dataNoti, dataMail };
      });
      //#endregion Update data
    } catch (ex) {
      LoggerCommon.error('[PlanningRequestRepository] updatePR error ', ex.message || ex);
      throw ex;
    }
  }

  async updatePRForGrantChart(id: string, body: UpdatePRForGrantChartDTO, user: TokenPayloadModel) {
    const preparedData = {
      id,
      plannedFromDate: body.plannedFromDate,
      plannedToDate: body.plannedToDate,
    };
    if (body.auditorIds) {
      Object.assign(preparedData, {
        auditors: body.auditorIds?.map((auditorId) => ({ id: auditorId } as User)),
      });
      if (body.auditorIds.length === 1) {
        Object.assign(preparedData, {
          leadAuditorId: body.auditorIds[0],
        });
      }
      if ((body.auditorIds.length > 1 && body.isLeadInspector) || !body.auditorIds.length) {
        Object.assign(preparedData, {
          leadAuditor: null,
        });
      }
    }

    if (body.auditCompanyId) {
      Object.assign(preparedData, { auditCompanyId: body.auditCompanyId });
    }

    if (body.departmentIds) {
      await this.manager
        .getCustomRepository(DepartmentRepository)
        .checkInactiveDepartment(body.departmentIds, user);
      Object.assign(preparedData, {
        departments: body.departmentIds?.map(
          (departmentId) => ({ id: departmentId } as Department),
        ),
      });
    }

    if (body.vesselId) {
      Object.assign(preparedData, {
        vesselId: body.vesselId,
      });
    }

    return this.save(preparedData);
  }

  async deletePlanningRequest(id: string, token: TokenPayloadModel) {
    try {
      const res = await this.delete({
        id,
        status: In([PlanningRequestStatus.DRAFT, PlanningRequestStatus.REJECTED]),
        companyId: token.companyId,
      });
      if (res.affected === 0) {
        throw new BaseError({ message: 'planningRequest.NOT_FOUND' });
      }
      return 1;
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] deletePlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async reviewPlanningRequest(
    planningRequestId: string,
    body: ReviewPRBodyDTO,
    token: TokenPayloadModel,
    workflowPermissions: string[],
  ) {
    const { currentReviewerStep } = body;
    const dataNoti: INotificationEventModel[] = [];
    const dataMail: IEmailEventModel[] = [];
    const PRFound = await this.createQueryBuilder('planningRequest')
      .leftJoinAndSelect('planningRequest.planningRequestHistories', 'planningRequestHistories')
      .where('planningRequest.id = :id AND planningRequest.companyId = :companyId', {
        id: planningRequestId,
        companyId: token.companyId,
      })
      .orderBy('planningRequestHistories.createdAt', 'DESC')
      .getOne();
    if (!PRFound) {
      throw new BaseError({ message: 'planningRequest.NOT_FOUND' });
    }

    const planningStatus = [
      PlanningRequestStatus.SUBMITTED,
      PlanningRequestStatus.REVIEWED_1,
      PlanningRequestStatus.REVIEWED_2,
      PlanningRequestStatus.REVIEWED_3,
      PlanningRequestStatus.REVIEWED_4,
      PlanningRequestStatus.REVIEWED_5,
    ];
    const lastReviewedStep = Number(PRFound.status.substring(PRFound.status.length - 1)) || 0;
    let status: string, previousStatus: string;
    if (PRFound.status === PlanningRequestStatus.SUBMITTED) {
      if (!workflowPermissions.includes(WorkflowPermission.REVIEWER1)) {
        throw new ForbiddenError({});
      }
      status = planningStatus[currentReviewerStep];
      previousStatus = planningStatus[currentReviewerStep - 1];
    } else if (PRFound.status.startsWith('reviewed_')) {
      const currentReviewedStep = currentReviewerStep;
      if (currentReviewedStep > 5) {
        throw new BaseError({ message: 'planningRequest.CANNOT_REVIEWED_5' });
      }

      // get max workflow role of user exam: review_5
      const maxRoleOfCurrentUser = workflowPermissions.reduce((currentMaxRole, role) => {
        if (role.startsWith('reviewer') && role > currentMaxRole) {
          return role;
        } else {
          return currentMaxRole;
        }
      }, WorkflowPermission.REVIEWER1);

      // get last char Number to compared 2 role, if less than current PR role then throw err
      const lastChar = maxRoleOfCurrentUser.slice(-1);

      if (lastChar <= PRFound.status.slice(-1)) {
        throw new BaseError({
          status: 404,
          message: 'planningRequest.PERMISSION_REQUIRED',
        });
      }

      // set status for PR and format type of enum string. exp: reviewer1 -> review_1
      // let selectedStep: number;
      // if (workflowPermissions.includes(`reviewer${currentReviewedStep}`)) {
      //   selectedStep = currentReviewedStep;
      // } else {
      //   selectedStep = Number(lastChar);
      // }
      status = planningStatus[currentReviewerStep];
      previousStatus = planningStatus[currentReviewerStep - 1];
    }

    if (PRFound.status === PlanningRequestStatus.REJECTED) {
      status = PRFound.previousStatus;
      if (PRFound.previousStatus === PlanningRequestStatus.REJECTED) {
        const preReassignStatus = this._getPreviousReassignStatus(PRFound.planningRequestHistories);
        const numberReview = Number(preReassignStatus.substring(preReassignStatus.length - 1));
        status =
          preReassignStatus === PlanningRequestStatus.REVIEWED_1
            ? PlanningRequestStatus.SUBMITTED
            : ((preReassignStatus.substring(0, preReassignStatus.length - 1) +
                (numberReview - 1)) as PlanningRequestStatus);
      }

      previousStatus = PlanningRequestStatus.REJECTED;
    }
    let reviewer = 0;
    let approver = false;
    if (
      PRFound.status === PlanningRequestStatus.REJECTED &&
      PRFound.previousStatus === PlanningRequestStatus.REJECTED
    ) {
      body.userAssignment?.usersPermissions.map((user) => {
        if (
          user?.permission != 'creator' &&
          user?.permission != 'auditor' &&
          user?.permission != 'owner/manager'
        ) {
          if (user?.permission != 'approver' && user?.userIds?.length > 0) {
            reviewer = Number(user?.permission[user.permission.length - 1]);
          } else if (user?.userIds?.length > 0) {
            approver = true;
          }
        }
      });
    }
    if (
      PRFound.status === PlanningRequestStatus.REJECTED &&
      PRFound.previousStatus === PlanningRequestStatus.REJECTED
    ) {
      if (approver) {
        status = `reviewed_${reviewer}`;
        previousStatus = PlanningRequestStatus.REJECTED;
      } else {
        status = `reviewed_${reviewer - 1}`;
        previousStatus = PlanningRequestStatus.REJECTED;
      }
    }
    const currentStatus = status;
    return await this.connection.transaction(async (manager) => {
      await manager.update(
        PlanningRequest,
        {
          id: planningRequestId,
        },
        {
          status,
          reassignFrom: null,
          previousStatus,
          updatedUserId: token.id,
        },
      );
      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(token.id);
      let plan = [];
      if (
        (lastReviewedStep > 0 && currentReviewerStep > 0) ||
        PRFound.status === PlanningRequestStatus.SUBMITTED
      ) {
        Array.from({ length: currentReviewerStep - lastReviewedStep }, (_, index) => {
          plan.push({
            planningRequestId,
            remark: body.comment,
            status: `reviewed_${lastReviewedStep + index + 1}`,
            createdUser,
          });
        });
      }
      if (
        PRFound.status === PlanningRequestStatus.REJECTED &&
        PRFound.previousStatus === PlanningRequestStatus.REJECTED
      ) {
        Array.from({ length: reviewer - currentReviewerStep }, (_, index) => {
          plan.push({
            planningRequestId,
            remark: body.comment,
            status: `reviewed_${currentReviewerStep + index}`,
            createdUser,
          });
        });
      }
      if (plan?.length > 0) {
        await manager.insert(PlanningRequestHistory, plan);
      }
      // Handle add Audit log
      // await manager.getCustomRepository(AuditLogRepository).createAuditLog(
      //   {
      //     module: AuditModuleEnum.PLANNING,
      //     planningId: planningRequestId,
      //   },
      //   token,
      //   [AuditActivityEnum.REVIEWED],
      //   createdUser,
      // );
      let listReceiverNoti = [];
      let nextStep;
      // Update user assignment
      if (body.userAssignment) {
        await manager
          .getCustomRepository(UserAssignmentRepository)
          .updateUserAssignment(
            manager,
            ModuleType.PLANNING_REQUEST,
            planningRequestId,
            body.userAssignment.usersPermissions,
            status,
          );

        const userPermissionUpdate = this._getListReviewUAIds(
          body.userAssignment.usersPermissions,
          currentStatus as PlanningRequestStatus,
        );

        await manager
          .getCustomRepository(UserAssignmentRepository)
          ._acceptEditPermission(userPermissionUpdate.userIds, planningRequestId, [
            userPermissionUpdate.permission,
          ] as WorkflowPermission[]);
        nextStep = userPermissionUpdate.permission as WorkflowPermission;
      }

      //#region Handle push noti
      // list User Assignment
      const listUserAssignment = await manager
        .getCustomRepository(UserAssignmentRepository)
        .listByModule(ModuleType.PLANNING_REQUEST, planningRequestId);

      // console.log('listUserAssignment', listUserAssignment);
      const { step1, step2, step3, step4, step5, stepApprove } = this.getStepsForAssigments(
        listUserAssignment,
      );
      // console.log('step2', step2);
      // console.log('step3', step3);
      // console.log('step4', step4);
      // console.log('step5', step5);
      // console.log('stepApprove', stepApprove);
      // console.log('PRFound.status', PRFound.status);

      if (nextStep) {
        listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
      }
      // console.log('listUserAssignment', listUserAssignment);
      // const step1 = listUserAssignment[WorkflowPermission.REVIEWER1].length ? 'reviewer1' : null;
      // const step2 = listUserAssignment[WorkflowPermission.REVIEWER2].length ? 'reviewer2' : null;
      // const step3 = listUserAssignment[WorkflowPermission.REVIEWER3].length ? 'reviewer3' : null;
      // const step4 = listUserAssignment[WorkflowPermission.REVIEWER4].length ? 'reviewer4' : null;
      // const step5 = listUserAssignment[WorkflowPermission.REVIEWER5].length ? 'reviewer5' : null;
      // const stepApprove = listUserAssignment[WorkflowPermission.APPROVER].length
      //   ? 'approver'
      //   : null;

      // let nextStep;
      // // console.log('step2', step2);
      // // console.log('step3', step3);
      // // console.log('step4', step4);
      // // console.log('step5', step5);
      // // console.log('stepApprove', stepApprove);
      // // console.log('PRFound.status', PRFound.status);

      // switch (PRFound.status) {
      //   case PlanningRequestStatus.SUBMITTED:
      //     nextStep = step1 || step2 || step3 || step4 || step5;

      //     if (nextStep) {
      //       listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
      //     }
      //     break;
      //   case PlanningRequestStatus.REVIEWED_1:
      //     if (status === PlanningRequestStatus.REVIEWED_2) {
      //       nextStep = step3 || step4 || step5 || stepApprove;
      //     }
      //     if (status === PlanningRequestStatus.REVIEWED_3) {
      //       nextStep = step4 || step5 || stepApprove;
      //     }

      //     if (status === PlanningRequestStatus.REVIEWED_4) {
      //       nextStep = step5 || stepApprove;
      //     }

      //     if (status === PlanningRequestStatus.REVIEWED_5) {
      //       nextStep = stepApprove;
      //     }
      //     if (nextStep) {
      //       listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
      //     }
      //     break;
      //   case PlanningRequestStatus.REVIEWED_2:
      //     if (status === PlanningRequestStatus.REVIEWED_3) {
      //       nextStep = step4 || step5 || stepApprove;
      //     }

      //     if (status === PlanningRequestStatus.REVIEWED_4) {
      //       nextStep = step5 || stepApprove;
      //     }

      //     if (status === PlanningRequestStatus.REVIEWED_5) {
      //       nextStep = stepApprove;
      //     }
      //     if (nextStep) {
      //       listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
      //     }
      //     break;
      //   case PlanningRequestStatus.REVIEWED_3:
      //     if (status === PlanningRequestStatus.REVIEWED_4) {
      //       nextStep = step5 || stepApprove;
      //     }

      //     if (status === PlanningRequestStatus.REVIEWED_5) {
      //       nextStep = stepApprove;
      //     }
      //     if (nextStep) {
      //       listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
      //     }
      //     break;
      //   case PlanningRequestStatus.REVIEWED_4:
      //     nextStep = stepApprove;
      //     if (nextStep) {
      //       listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
      //     }
      //     break;
      //   case PlanningRequestStatus.REVIEWED_5:
      //     listReceiverNoti = listReceiverNoti.concat(
      //       listUserAssignment[WorkflowPermission.APPROVER],
      //     );
      //     break;
      // }
      const performer = await manager
        .getCustomRepository(UserRepository)
        .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle', 'email']);
      dataNoti.push({
        receivers: listReceiverNoti,
        module: ModuleType.PLANNING_REQUEST,
        recordId: PRFound.id,
        recordRef: PRFound.refId,
        type: PushTypeEnum.CHANGE_RECORD_STATUS,
        currentStatus: status,
        previousStatus,
        performer: performer,
        executedAt: new Date(),
      });

      for (const receiver of listReceiverNoti) {
        dataMail.push({
          receiver: receiver as IUserEmail,
          type: EmailTypeEnum.CHANGE_RECORD_STATUS,
          templateKey: MailTemplate.CHANGE_RECORD_STATUS,
          subject: '[Notification] Change status in a record',
          data: {
            username: receiver.username,
            baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
            recordRef: PRFound.refId,
            recordId: PRFound.id,
            path: ModulePathEnum.PLANNING_AND_REQUEST,
            currentStatus: status,
            previousStatus,
            performer: performer,
            executedAt: new Date(),
          },
        });
      }
      //#endregion Handle push noti
      return { dataNoti, dataMail };
    });
  }
  getStepsForAssigments(listUserAssignment: {}): {
    step1: any;
    step2: any;
    step3: any;
    step4: any;
    step5: any;
    stepApprove: any;
  } {
    const step1 = listUserAssignment[WorkflowPermission.REVIEWER1].length ? 'reviewer1' : null;
    const step2 = listUserAssignment[WorkflowPermission.REVIEWER2].length ? 'reviewer2' : null;
    const step3 = listUserAssignment[WorkflowPermission.REVIEWER3].length ? 'reviewer3' : null;
    const step4 = listUserAssignment[WorkflowPermission.REVIEWER4].length ? 'reviewer4' : null;
    const step5 = listUserAssignment[WorkflowPermission.REVIEWER5].length ? 'reviewer5' : null;
    const stepApprove = listUserAssignment[WorkflowPermission.APPROVER].length ? 'approver' : null;
    return { step1, step2, step3, step4, step5, stepApprove };
  }

  async approvePlanningRequest(
    planningRequestId: string,
    body: RejectPRBodyDTO,
    token: TokenPayloadModel,
  ) {
    try {
      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(token.id);
      const dataNoti: INotificationEventModel[] = [];
      const dataMail: IEmailEventModel[] = [];

      return this.connection.transaction(async (manager) => {
        const PRFound = await this.detailByConditions(
          {
            id: planningRequestId,
            companyId: token.companyId,
            status: PlanningRequestStatus.SUBMITTED,
          },
          [
            'planningRequestOfficeComments',
            'planningRequestAdditionalReviewers',
            'userAssignments',
          ],
        );
        if (!PRFound) {
          throw new BaseError({ message: 'planningRequest.NOT_FOUND' });
        }
        //check can approve
        await Promise.all([
          manager.update(
            PlanningRequest,
            {
              id: planningRequestId,
              companyId: token.companyId,
              status: PlanningRequestStatus.SUBMITTED,
            },
            {
              status: PlanningRequestStatus.APPROVED,
              previousStatus: PlanningRequestStatus.SUBMITTED,
            },
          ),
          manager.insert(PlanningRequestHistory, {
            planningRequestId,
            remark: body.comment,
            status: PlanningRequestStatus.APPROVED,
            createdUser,
          }),
        ]);

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: planningRequestId,
          },
          token,
          [AuditActivityEnum.APPROVED],
          createdUser,
        );

        // Update user assignment
        await manager
          .getCustomRepository(UserAssignmentRepository)
          .updateUserAssignment(
            manager,
            ModuleType.PLANNING_REQUEST,
            planningRequestId,
            body.userAssignment.usersPermissions,
          );
        const updateAssignmentIds = PRFound.userAssignments.map((user) => user.id);
        await manager
          .getCustomRepository(UserAssignmentRepository)
          ._updateEditPermission(updateAssignmentIds, planningRequestId);

        // Prepare Data mail and noti
        const listUserAssignment = await manager
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.PLANNING_REQUEST, planningRequestId);

        let listReceiverNotiAndMail = [];
        listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
          listUserAssignment[WorkflowPermission.AUDITOR],
        );
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle']);

        dataNoti.push({
          receivers: listReceiverNotiAndMail,
          module: ModuleType.PLANNING_REQUEST,
          recordId: planningRequestId,
          recordRef: PRFound.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: PlanningRequestStatus.APPROVED,
          previousStatus: PlanningRequestStatus.SUBMITTED,
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNotiAndMail) {
          dataMail.push({
            receiver: {
              email: receiver.email,
            },
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: PRFound.refId,
              recordId: planningRequestId,
              path: ModulePathEnum.PLANNING_AND_REQUEST,
              currentStatus: PlanningRequestStatus.APPROVED,
              previousStatus: PlanningRequestStatus.SUBMITTED,
            },
          });
        }
        return { dataNoti, dataMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] approvePlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async rejectPlanningRequest(id: string, params: RejectPRBodyDTO, token: TokenPayloadModel) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataMail: IEmailEventModel[] = [];
      const PRFound = await this.createQueryBuilder('planningRequest')
        .where('(planningRequest.id = :id AND planningRequest.companyId = :companyId)', {
          id,
          companyId: token.companyId,
        })
        .leftJoin('planningRequest.createdUser', 'createdUser')
        .leftJoin('planningRequest.planningRequestHistories', 'prHistories')
        .leftJoin('planningRequest.userAssignments', 'userAssignments')
        .select([
          'planningRequest.id',
          'planningRequest.status',
          'planningRequest.refId',
          'planningRequest.createdUserId',
          'planningRequest.reassignFrom',
          'userAssignments',
        ])
        .addSelect([
          'createdUser.id',
          'createdUser.username',
          'createdUser.jobTitle',
          'createdUser.email',
          'prHistories',
        ])
        .orderBy('prHistories.createdAt', 'DESC')
        .getOne();

      if (!PRFound) {
        throw new BaseError({ message: 'planningRequest.NOT_FOUND' });
      }

      let previousStatusReassign;

      return this.connection.transaction(async (manager) => {
        let previousStatus: string;

        switch (PRFound.status) {
          case PlanningRequestStatus.SUBMITTED:
            previousStatus = PlanningRequestStatus.SUBMITTED;
            previousStatusReassign = PlanningRequestStatus.REVIEWED_1;
            break;
          case PlanningRequestStatus.APPROVED:
            previousStatus = PlanningRequestStatus.CANCELLED;
            previousStatusReassign = PlanningRequestStatus.CANCELLED;
            break;
          case PlanningRequestStatus.AUDITOR_ACCEPTED:
            previousStatus = PlanningRequestStatus.AUDITOR_ACCEPTED;
            previousStatusReassign = PlanningRequestStatus.AUDITOR_ACCEPTED;
            break;
          case PlanningRequestStatus.REVIEWED_1:
            previousStatus = PlanningRequestStatus.REVIEWED_1;
            previousStatusReassign = PlanningRequestStatus.REVIEWED_2;
            break;
          case PlanningRequestStatus.REVIEWED_2:
            previousStatus = PlanningRequestStatus.REVIEWED_2;
            previousStatusReassign = PlanningRequestStatus.REVIEWED_3;
            break;
          case PlanningRequestStatus.REVIEWED_3:
            previousStatus = PlanningRequestStatus.REVIEWED_3;
            previousStatusReassign = PlanningRequestStatus.REVIEWED_4;
            break;
          case PlanningRequestStatus.REVIEWED_4:
            previousStatus = PlanningRequestStatus.REVIEWED_4;
            previousStatusReassign = PlanningRequestStatus.REVIEWED_5;
            break;
          case PlanningRequestStatus.REVIEWED_5:
            previousStatus = PlanningRequestStatus.REVIEWED_5;
            previousStatusReassign = PlanningRequestStatus.APPROVED;
            break;
          case PlanningRequestStatus.REJECTED:
            previousStatus = PlanningRequestStatus.REJECTED;

            const reassignFrom = PRFound.reassignFrom;
            if (!reassignFrom) {
              previousStatusReassign = this._getPreviousReassignStatus(
                PRFound.planningRequestHistories,
              );
            } else if (reassignFrom === PlanningRequestStatus.APPROVED) {
              previousStatusReassign = PlanningRequestStatus.REVIEWED_5;
            } else {
              const numberUserReassign = reassignFrom.slice(-1);
              previousStatusReassign = `reviewed_${+numberUserReassign - 1}`;
            }

            break;
          default:
            break;
        }

        const userAssignmentsUpdate = this._getListUpdateUAIds(
          PRFound.userAssignments,
          previousStatusReassign,
        );
        const userApporover = PRFound.userAssignments.filter(
          (user) => user.permission === WorkflowPermission.APPROVER,
        );
        const updateUserAssignmentIds = userAssignmentsUpdate.length
          ? userAssignmentsUpdate
          : userApporover.map((x) => x.id);

        await manager.update(
          PlanningRequest,
          { id },
          {
            status: PlanningRequestStatus.REJECTED,
            reassignFrom: previousStatusReassign,
            previousStatus,
          },
        );

        await manager
          .getCustomRepository(UserAssignmentRepository)
          ._updateEditPermission(updateUserAssignmentIds, PRFound.id);

        // Get user info
        const createdUser = await manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);

        // Create History
        await manager.insert(PlanningRequestHistory, {
          planningRequestId: id,
          remark: params.comment,
          status: PlanningRequestStatus.REJECTED,
          createdUser,
        });

        // Create Additional reviewer
        await manager.insert(PRAdditionalReviewer, {
          planningRequestId: id,
          comment: params.comment,
          reviewedDate: new Date(),
          createdUserId: token.id,
        });

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: id,
          },
          null,
          [AuditActivityEnum.REASSIGNED],
          createdUser,
        );

        // Update user assignment
        if (params.userAssignment) {
          await manager
            .getCustomRepository(UserAssignmentRepository)
            .updateUserAssignment(
              manager,
              ModuleType.PLANNING_REQUEST,
              id,
              params.userAssignment.usersPermissions,
              null,
              previousStatusReassign,
            );
        }

        // Prepare Mail and Noti data
        const prevStatusNoti =
          previousStatus === PlanningRequestStatus.REJECTED // STATUS FOR NOTI
            ? CONVERT_STATUS.REASSIGNED
            : previousStatus;
        const statusNoti = CONVERT_STATUS.REASSIGNED;
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle']);

        dataNoti.push({
          receivers: [PRFound.createdUser],
          module: ModuleType.PLANNING_REQUEST,
          recordId: id,
          recordRef: PRFound.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: statusNoti,
          previousStatus: prevStatusNoti,
          performer,
          executedAt: new Date(),
        });

        dataMail.push({
          receiver: {
            email: PRFound.createdUser.email,
          },
          type: EmailTypeEnum.CHANGE_RECORD_STATUS,
          templateKey: MailTemplate.CHANGE_RECORD_STATUS,
          subject: '[Notification] Change status in a record',
          data: {
            username: PRFound.createdUser.username,
            baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
            recordRef: PRFound.refId,
            recordId: id,
            path: ModulePathEnum.PLANNING_AND_REQUEST,
            currentStatus: statusNoti,
            previousStatus: prevStatusNoti,
          },
        });

        return { dataNoti, dataMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] rejectPlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async auditorAcceptPlanningRequest(id: string, body: AcceptPRBodyDTO, token: TokenPayloadModel) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataMail: IEmailEventModel[] = [];
      const PRFound = await this.getOneQB(
        this.createQueryBuilder('planningRequest')
          .leftJoin('planningRequest.auditors', 'auditors')
          .where(
            '(planningRequest.id = :id AND planningRequest.companyId = :companyId AND planningRequest.status = :status)',
            {
              id,
              companyId: token.companyId,
              status: PlanningRequestStatus.APPROVED,
            },
          )
          .select(['planningRequest.id', 'planningRequest.leadAuditorId', 'planningRequest.refId'])
          .addSelect(['auditors.id']),
      );

      if (!PRFound) {
        throw new BaseError({ message: 'planningRequest.NOT_FOUND' });
      }

      if (PRFound.auditors?.length == 0 || !PRFound.leadAuditorId) {
        throw new BaseError({ message: 'planningRequest.INSPECTORS_REQUIRED' });
      }

      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(token.id);

      return this.connection.transaction(async (manager) => {
        await Promise.all([
          manager.update(
            PlanningRequest,
            { id },
            {
              status: PlanningRequestStatus.AUDITOR_ACCEPTED,
              previousStatus: PlanningRequestStatus.APPROVED,
            },
          ),
          manager.insert(PlanningRequestHistory, {
            planningRequestId: id,
            remark: body.comment,
            status: PlanningRequestStatus.AUDITOR_ACCEPTED,
            createdUser,
          }),
        ]);

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: id,
          },
          token,
          [AuditActivityEnum.ACCEPTED],
          createdUser,
        );

        // Update user assignment
        await manager
          .getCustomRepository(UserAssignmentRepository)
          .updateUserAssignment(
            manager,
            ModuleType.PLANNING_REQUEST,
            id,
            body.userAssignment.usersPermissions,
          );

        // Prepare data mail and noti
        const listUserAssignment = await manager
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.PLANNING_REQUEST, id);

        let listReceiverNotiAndMail = [];
        listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
          listUserAssignment[WorkflowPermission.OWNER_MANAGER],
        );
        listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
          listUserAssignment[WorkflowPermission.APPROVER],
        );
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle']);

        dataNoti.push({
          receivers: listReceiverNotiAndMail,
          module: ModuleType.PLANNING_REQUEST,
          recordId: id,
          recordRef: PRFound.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: 'auditor accepted',
          previousStatus: PlanningRequestStatus.APPROVED,
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNotiAndMail) {
          // console.log('auditor_accept: ', receiver);

          dataMail.push({
            receiver: {
              email: receiver.email,
            },
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: PRFound.refId,
              recordId: id,
              path: ModulePathEnum.PLANNING_AND_REQUEST,
              currentStatus: 'auditor accepted',
              previousStatus: PlanningRequestStatus.APPROVED,
            },
          });
        }
        return { dataNoti, dataMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] auditorAcceptPlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async auditeeAcceptPlanningRequest(id: string, body: AcceptPRBodyDTO, token: TokenPayloadModel) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataMail: IEmailEventModel[] = [];
      const PRFound = await this.getOneQB(
        this.createQueryBuilder('planningRequest')
          .leftJoin('planningRequest.auditors', 'auditors')
          .leftJoinAndSelect('planningRequest.auditTypes', 'auditTypes')
          .leftJoinAndSelect('planningRequest.pRFocusRequests', 'pRFocusRequests')
          .leftJoinAndSelect(
            'planningRequest.planningRequestOfficeComments',
            'planningRequestOfficeComments',
          )
          .leftJoinAndSelect('planningRequest.userAssignments', 'userAssignments')
          .where(
            '(planningRequest.id = :id AND planningRequest.companyId = :companyId AND planningRequest.status IN (:...status))',
            {
              id,
              companyId: token.companyId,
              status: [
                PlanningRequestStatus.AUDITOR_ACCEPTED,
                PlanningRequestStatus.SUBMITTED,
                PlanningRequestStatus.REVIEWED_1,
                PlanningRequestStatus.REVIEWED_2,
                PlanningRequestStatus.REVIEWED_3,
                PlanningRequestStatus.REVIEWED_4,
                PlanningRequestStatus.REVIEWED_5,
              ],
            },
          )
          .select()
          .addSelect(['auditors.id', 'auditors.username', 'auditors.jobTitle', 'auditors.email']),
      );

      if (!PRFound) {
        throw new BaseError({ message: 'planningRequest.NOT_FOUND' });
      }

      if (PRFound.auditors?.length == 0 || !PRFound.leadAuditorId) {
        throw new BaseError({ message: 'planningRequest.MANDATORY_FIELDS_REQUIRED' });
      }

      const dueDateAnDateOfLastInspectionPR = await this.manager.find(
        PRDueDateAndDateOfLastInspection,
        {
          where: { deleted: false, planningRequestId: PRFound.id },
          select: ['id', 'dueDate'],
        },
      );

      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(token.id);

      const unplannedPRId = Utils.strings.generateUUID();
      const dueDateMax = maxBy(dueDateAnDateOfLastInspectionPR, function (o: any) {
        return o?.dueDate;
      });
      const unplannedPR = {
        ...PRFound,
        id: unplannedPRId,
        plannedFromDate: dueDateMax?.dueDate || PRFound?.plannedFromDate,
        estimatedTimeDeparture: dueDateMax?.dueDate || PRFound?.estimatedTimeDeparture,
        toPortEstimatedTimeArrival: dueDateMax?.dueDate || PRFound?.toPortEstimatedTimeArrival,
        toPortEstimatedTimeDeparture: dueDateMax?.dueDate || PRFound?.toPortEstimatedTimeDeparture,
        fromPortEstimatedTimeArrival: dueDateMax?.dueDate || PRFound?.fromPortEstimatedTimeArrival,
        fromPortEstimatedTimeDeparture:
          dueDateMax?.dueDate || PRFound?.fromPortEstimatedTimeDeparture,
        estimatedTimeArrival: dueDateMax?.dueDate || PRFound?.estimatedTimeArrival,
        plannedToDate: dueDateMax?.dueDate || PRFound?.plannedToDate,
        status: PlanningRequestStatus.DRAFT,
        previousStatus: PlanningRequestStatus.APPROVED,
        createdAt: new Date(Date.now()),
        leadAuditorId: null,
        auditors: [] as User[],
        auditTypes: PRFound.auditTypes.map((auditType) => auditType as AuditType),
        pRFocusRequests: PRFound.pRFocusRequests.map((pRFocusRequest) => {
          return {
            ...pRFocusRequest,
            planningRequestId: unplannedPRId,
            id: Utils.strings.generateUUID(),
          } as PRFocusRequest;
        }),
        planningRequestOfficeComments: PRFound.planningRequestOfficeComments.map(
          (officeComments) => {
            return {
              ...officeComments,
              planningRequestId: unplannedPRId,
              id: Utils.strings.generateUUID(),
            } as PROfficeComment;
          },
        ),
        attachments: decryptAttachmentValues(PRFound.attachments),
      };

      return this.connection.transaction(async (manager) => {
        // Update PR to PLANNED_SUCCESSFULLY, create History
        const { currentReviewerStep } = body;
        const planningItem = [];
        const planningStatus = [
          PlanningRequestStatus.REVIEWED_1,
          PlanningRequestStatus.REVIEWED_2,
          PlanningRequestStatus.REVIEWED_3,
          PlanningRequestStatus.REVIEWED_4,
          PlanningRequestStatus.REVIEWED_5,
        ];
        await manager.update(
          PlanningRequest,
          { id },
          {
            status: PlanningRequestStatus.APPROVED,
            previousStatus: currentReviewerStep
              ? PRStatusFilter.REVIEWED + `_${currentReviewerStep}`
              : PRFound?.status,
          },
        );
        if (PRFound.status === PlanningRequestStatus.SUBMITTED) {
          Array.from({ length: currentReviewerStep }, (_, index) => {
            planningItem.push({
              planningRequestId: id,
              remark: body.comment,
              status: planningStatus[index],
              createdUser,
            });
          });
        }
        if (PRFound?.status?.startsWith('reviewed_') && currentReviewerStep) {
          Array.from(
            {
              length:
                currentReviewerStep - Number(PRFound.status.substring(PRFound.status.length - 1)),
            },
            (_, index) => {
              planningItem.push({
                planningRequestId: id,
                remark: body.comment,
                status: planningStatus[currentReviewerStep - 1],
                createdUser,
              });
            },
          );
        }
        if (body.isDirectApproved) {
          planningItem.push({
            planningRequestId: id,
            remark: body.comment,
            status: PlanningRequestStatus.APPROVED,
            createdUser,
          });
        }
        await manager.insert(PlanningRequestHistory, planningItem);

        // trigger create unplanned PR if due date exists
        const hasDueDate = dueDateAnDateOfLastInspectionPR?.some(
          (dueDateAnDateOfLastInspection) => dueDateAnDateOfLastInspection?.dueDate,
        );

        if (hasDueDate) {
          const currYear = momentTZ.tz(body.timezone).year();
          const counter = await this.manager
            .getCustomRepository(CompanyFeatureVersionRepository)
            .getNextVersion({
              manager: manager,
              companyId: token.companyId,
              feature: FeatureVersionConfig.PLANNING_REQUEST_COUNTER,
              year: Number(currYear),
            });
          const company = await this.manager.findOne(Company, {
            where: { id: token.companyId },
            select: ['code'],
          });

          const serialNumber = leadingZero(counter, 3);

          await manager.save(PlanningRequest, {
            ...unplannedPR,
            refId: `${company.code}/PL/${serialNumber}/${currYear}`,
            auditNo: `PL${company.code}${currYear}${serialNumber}`,
          });

          await manager.insert(PlanningRequestHistory, {
            planningRequestId: unplannedPR.id,
            status: PlanningRequestStatus.DRAFT,
            createdUser,
          });

          await manager.insert(PRFocusRequest, unplannedPR.pRFocusRequests);
          if (PRFound.planningRequestOfficeComments?.length > 0) {
            await manager.insert(PROfficeComment, unplannedPR.planningRequestOfficeComments);
          }

          // Handle add Audit log: unplanned
          await manager.getCustomRepository(AuditLogRepository).createAuditLog(
            {
              module: AuditModuleEnum.PLANNING,
              planningId: unplannedPR.id,
            },
            token,
            [AuditActivityEnum.CREATED],
            createdUser,
          );
        }

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: id,
          },
          token,
          [AuditActivityEnum.PLANNED],
          createdUser,
        );
        // Trigger create audit workspace
        if (PRFound?.isSA == true) {
          await manager
            .getCustomRepository(AuditWorkspaceRepository)
            ._triggerCreateAuditWorkspaceForSA(
              manager,
              {
                planningRequestId: id,
                timezone: body.timezone,
                auditTypeIds: PRFound.auditTypes.map((auditType) => auditType.id),
              },
              token,
              createdUser,
            );
        } else {
          await manager.getCustomRepository(AuditWorkspaceRepository)._triggerCreateAuditWorkspace(
            manager,
            {
              planningRequestId: id,
              timezone: body.timezone,
              auditTypeIds: PRFound.auditTypes.map((auditType) => auditType.id),
            },
            token,
            createdUser,
          );
        }
        // Trigger create internal audit report
        await manager
          .getCustomRepository(InternalAuditReportRepository)
          ._triggerCreateInternalAuditReport(
            manager,
            {
              //reportFindingFormId: null,
              planningRequestId: id,
              vesselId: PRFound.vesselId,
              departmentId: PRFound.departmentId,
              timezone: body.timezone,
            },
            token,
            createdUser,
          );
        //Edit permission
        const updateAssignments = [];
        PRFound.userAssignments?.forEach((user) => {
          user.isView = true;
          user.isEdit = user.permission == WorkflowPermission.CREATOR;
          updateAssignments.push(user);
        });
        await manager.getCustomRepository(UserAssignmentRepository).save(updateAssignments);

        // Prepare data mail and noti
        const listReceiverNotiAndMail = PRFound.auditors;
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle']);
        dataNoti.push({
          receivers: listReceiverNotiAndMail,
          module: ModuleType.PLANNING_REQUEST,
          recordId: id,
          recordRef: PRFound.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: PlanningRequestStatus.APPROVED, // planned_successfully
          previousStatus: PRFound.status, // auditor_accepted
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNotiAndMail) {
          dataMail.push({
            receiver: {
              email: receiver.email,
            },
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: PRFound.refId,
              recordId: id,
              path: ModulePathEnum.PLANNING_AND_REQUEST,
              currentStatus: PlanningRequestStatus.APPROVED, // planned_successfully
              previousStatus: PRFound.status, // auditor_accepted
            },
          });
        }
        return { dataNoti, dataMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] auditeeAcceptPlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async cancelPlanningRequest(id: string, body: RejectPRBodyDTO, token: TokenPayloadModel) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataMail: IEmailEventModel[] = [];
      const PRFoundById = await this.createQueryBuilder('planningRequest')
        .where('planningRequest.id = :id AND planningRequest.companyId = :companyId', {
          id,
          companyId: token.companyId,
        })
        .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
        .leftJoin('planningRequest.internalAuditReport', 'internalAuditReport')
        .leftJoin('planningRequest.createdUser', 'createdUser')
        .leftJoin('planningRequest.userAssignments', 'userAssignments')
        .select()
        .addSelect([
          'auditWorkspace.id',
          'auditWorkspace.status',
          'internalAuditReport.id',
          'internalAuditReport.status',
          'createdUser.id',
          'createdUser.username',
          'createdUser.jobTitle',
          'createdUser.email',
          'userAssignments',
        ])
        .getOne();
      if (!PRFoundById) {
        throw new BaseError({ message: 'planningRequest.NOT_FOUND' });
      }
      if (PRFoundById.userAssignments.length) {
        const creator = PRFoundById.userAssignments.filter(
          (user) => user.permission === WorkflowPermission.CREATOR,
        )[0];

        const approver = PRFoundById.userAssignments
          .filter((user) => user.permission === WorkflowPermission.APPROVER)
          ?.map((item) => item.userId);

        if (
          token.id !== creator.userId &&
          (PRFoundById.status !== PlanningRequestStatus.APPROVED ||
            (PRFoundById.status === PlanningRequestStatus.APPROVED && !approver.includes(token.id)))
        ) {
          throw new BaseError({ message: 'planningRequest.CANNOT_CANCEL' });
        }
      }
      if (
        PRFoundById.auditWorkspace &&
        AuditWorkspaceStatus.NEW !== PRFoundById.auditWorkspace.status
      ) {
        throw new BaseError({ message: 'planningRequest.CANNOT_CANCEL' });
      }
      return this.connection.transaction(async (managerTrans) => {
        const createdUser = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);
        // Cancel PR
        await managerTrans.update(
          PlanningRequest,
          {
            id,
          },
          {
            status: PlanningRequestStatus.CANCELLED,
            previousStatus: PRFoundById.status,
          },
        );
        if (PRFoundById.auditWorkspace) {
          await Promise.all([
            // soft delete audit workspace
            managerTrans.update(
              AuditWorkspace,
              { id: PRFoundById.auditWorkspace.id },
              { deleted: true },
            ),
          ]);
        }

        if (PRFoundById?.internalAuditReport) {
          await Promise.all([
            // soft delete audit workspace
            managerTrans.update(
              InternalAuditReport,
              { id: PRFoundById.internalAuditReport.id },
              { deleted: true },
            ),
          ]);
        }

        await Promise.all([
          // create PR history
          managerTrans.insert(PlanningRequestHistory, {
            planningRequestId: id,
            remark: body.comment,
            status: PlanningRequestStatus.CANCELLED,
            createdUser: createdUser,
          }),
        ]);

        //Update global status
        await managerTrans.update(
          PlanningRequest,
          {
            id: id,
          },
          {
            globalStatus: GlobalStatusEnum.CANCELLED,
          },
        );

        // Handle add Audit log
        await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: id,
          },
          token,
          [AuditActivityEnum.CANCELLED],
          createdUser,
        );

        // Update user assignment
        await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .updateUserAssignment(
            managerTrans,
            ModuleType.PLANNING_REQUEST,
            id,
            body.userAssignment.usersPermissions,
          );
        const updateAssignmentIds = PRFoundById.userAssignments.map((user) => user.id);
        await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          ._updateEditPermission(updateAssignmentIds, id);
        // Prepare data mail and noti
        const listUserAssignment = await managerTrans
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.PLANNING_REQUEST, id);

        let listReceiverNotiAndMail = [];
        listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
          listUserAssignment[WorkflowPermission.APPROVER],
          listUserAssignment[WorkflowPermission.REVIEWER5],
          listUserAssignment[WorkflowPermission.REVIEWER1],
          listUserAssignment[WorkflowPermission.REVIEWER2],
          listUserAssignment[WorkflowPermission.REVIEWER3],
          listUserAssignment[WorkflowPermission.REVIEWER4],
        );
        listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
          listUserAssignment[WorkflowPermission.AUDITOR],
        );
        listReceiverNotiAndMail = listReceiverNotiAndMail.concat(
          listUserAssignment[WorkflowPermission.OWNER_MANAGER],
        );
        listReceiverNotiAndMail = listReceiverNotiAndMail.concat([PRFoundById.createdUser]);
        const performer = await managerTrans
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle']);
        dataNoti.push({
          receivers: listReceiverNotiAndMail,
          module: ModuleType.PLANNING_REQUEST,
          recordId: id,
          recordRef: PRFoundById.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: PlanningRequestStatus.CANCELLED,
          previousStatus: PRFoundById.status, // planned_successfully
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNotiAndMail) {
          dataMail.push({
            receiver: {
              email: receiver.email,
            },
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: PRFoundById.refId,
              recordId: id,
              path: ModulePathEnum.PLANNING_AND_REQUEST,
              currentStatus: PlanningRequestStatus.CANCELLED,
              previousStatus: PRFoundById.status, // planned_successfully
            },
          });
        }
        return { dataNoti, dataMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] cancelPlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async _triggerInProgressPlanningRequest(
    planningRequestId: string,
    userId: string,
    createdUser?: CreatedUserHistoryModel,
  ) {
    try {
      const updateRes = await this.manager.update(
        PlanningRequest,
        {
          id: planningRequestId,
          status: PlanningRequestStatus.APPROVED,
        },
        {
          status: PlanningRequestStatus.IN_PROGRESS,
          previousStatus: PlanningRequestStatus.APPROVED,
        },
      );

      if (updateRes.affected === 1) {
        // Get user info
        if (!createdUser) {
          createdUser = await this.manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(userId);
        }

        await this.manager.insert(PlanningRequestHistory, {
          planningRequestId,
          remark: null,
          status: PlanningRequestStatus.IN_PROGRESS,
          createdUser,
        });

        // Handle add Audit log
        await this.manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: planningRequestId,
          },
          null,
          [AuditActivityEnum.IN_PROGRESS],
          createdUser,
        );
      }

      return updateRes.affected;
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] _triggerInProgressPlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async _triggerCompletedPlanningRequest(
    planningRequestId: string,
    createdUser: CreatedUserHistoryModel,
    userId: string,
  ) {
    try {
      const updateRes = await this.manager.update(
        PlanningRequest,
        {
          id: planningRequestId,
          status: PlanningRequestStatus.IN_PROGRESS,
        },
        {
          status: PlanningRequestStatus.COMPLETED,
          previousStatus: PlanningRequestStatus.IN_PROGRESS,
        },
      );
      if (updateRes.affected === 1) {
        // Get user info
        createdUser =
          createdUser ||
          (await this.manager.getCustomRepository(UserRepository)._getUserInfoForHistory(userId));

        await this.manager.insert(PlanningRequestHistory, {
          planningRequestId,
          remark: null,
          status: PlanningRequestStatus.COMPLETED,
          createdUser,
        });

        // Handle add Audit log
        await this.manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.PLANNING,
            planningId: planningRequestId,
          },
          null,
          [AuditActivityEnum.COMPLETED],
          createdUser,
        );
      }

      return updateRes.affected;
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] _triggerCompletedPlanningRequest error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  // Get vessel and auditors by planning request. And check valid of PR also
  async _getVesselAndAuditorsByPR(companyId: string, planningRequestId: string) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .where(
        'planningRequest.id = :planningRequestId AND planningRequest.companyId = :companyId AND planningRequest.status IN (:...status)',
        {
          planningRequestId,
          companyId: companyId,
          status: [PlanningRequestStatus.APPROVED, PlanningRequestStatus.IN_PROGRESS],
        },
      )
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.vessel', 'vessel')
      //.leftJoin('planningRequest.department', 'department')
      .leftJoin('planningRequest.departments', 'departments')
      .select([
        'planningRequest.id',
        'planningRequest.vesselId',
        'planningRequest.departmentId',
        'departments.code',
        'planningRequest.entityType',
        'planningRequest.isSA',
        'planningRequest.auditCompanyId',
      ])
      .addSelect(['auditors.id']);

    const planningRequest = await this.getOneQB(queryBuilder);

    if (planningRequest) {
      return planningRequest;
    } else {
      throw new BaseError({ status: 400, message: 'planningRequest.INVALID' });
    }
  }

  async _checkAndGetPRForReportFinding(planningRequestId: string, user: TokenPayloadModel) {
    const qb = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .leftJoin('planningRequest.auditors', 'auditors')
      .where(
        'planningRequest.id = :planningRequestId AND auditTimeTable.status = :auditTimeTableStatus AND auditors.id = :userId',
        {
          planningRequestId,
          auditTimeTableStatus: AuditTimeTableStatus.SUBMITTED,
          userId: user.id,
        },
      )
      .select(['planningRequest.id', 'planningRequest.vesselId', 'planningRequest.leadAuditorId']);

    const planningRequest = await this.getOneQB(qb);

    if (planningRequest) {
      return planningRequest;
    } else {
      throw new BaseError({ status: 400, message: 'planningRequest.INVALID' });
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async _checkAndGetPROnTrigger(planningRequestId: string, user?: TokenPayloadModel | string) {
    const qb = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('vessel.country', 'country')
      // .leftJoin('vessel.fleet', 'fleet')
      .leftJoin('vessel.owners', 'owners')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      //.leftJoin('planningRequest.department', 'department')
      .leftJoin('planningRequest.departments', 'departments')
      .leftJoin('vessel.vesselType', 'vesselType')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      .leftJoin('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .leftJoin('planningRequest.company', 'company')
      .where('planningRequest.id = :planningRequestId', {
        // No need to check auditor
        planningRequestId,
      })
      .select([
        'planningRequest.id',
        'planningRequest.departmentId',
        'planningRequest.vesselId',
        'planningRequest.leadAuditorId',
        'planningRequest.fromPortId',
        'planningRequest.toPortId',
        'planningRequest.entityType',
        'vessel.name',
        // 'vessel.countryFlag',
        'vessel.countryId',
        'vessel.country',
        'country.id',
        'country.name',
        // 'vessel.fleetId',
        'vessel.vesselTypeId',
        // 'fleet.name',
        'vesselType.name',
        'auditors.id',
        'auditors.username',
        'fromPort.name',
        'toPort.name',
        'auditTypes.id',
        'auditTypes.name',
        'owners.id',
        'owners.username',
        'auditCompany.id',
        'departments.id',
        'departments.name',
        'auditCompany.name',
        'company.code',
      ]);

    const planningRequest = await this.getOneQB(qb);

    if (planningRequest) {
      return planningRequest;
    } else {
      throw new BaseError({ status: 400, message: 'planningRequest.INVALID' });
    }
  }

  async getPlanningRequestDataForCreatePdf(planingRequestId: string) {
    const rawQuery = `
                      SELECT
                      pr."auditNo" AS "serialNo",
                      pr."refId" AS "refNo",
                      (
                      SELECT
                        STRING_AGG(at2."name", ',')
                      FROM
                        (
                        SELECT
                          prat."auditTypeId"
                        FROM
                          planning_request_audit_type prat
                        WHERE
                          prat."planningRequestId" = $1) AS tem
                      JOIN audit_type at2
                          ON
                        at2.id = tem."auditTypeId") AS "auditType",
                      pr."plannedFromDate" AS "auditFromDate",
                      pr."plannedToDate" AS "auditToDate",
                      fromport."name" AS "fromPort",
                      toport."name" AS "toPort",
                      pr."fromPortEstimatedTimeArrival" AS "fromPortETA",
                      pr."toPortEstimatedTimeArrival" AS "toPortETA",
                      pr."fromPortEstimatedTimeDeparture" AS "fromPortETD",
                      pr."toPortEstimatedTimeDeparture" AS "toPortETD",
                      pr."typeOfAudit" AS "visitType",
                      pr."workingType" AS "workingType",
                      pr."memo" AS "memo",
                      pr."entityType" AS "entityType",
                      c."name" AS "companyName",
                      (
                      SELECT
                        leadauditor.username
                      FROM
                        "user" leadauditor
                      WHERE
                        leadauditor."id" = pr."leadAuditorId") AS "leadAuditor",
                      (
                      SELECT
                        STRING_AGG(auditor."username", ',')
                      FROM
                        (
                        SELECT
                          pra."userId"
                        FROM
                          planning_request_auditor pra
                        WHERE
                          pra."planningRequestId" = $1
                      ) AS tem
                      JOIN "user" auditor
                      ON
                        auditor.id = tem."userId"
                    ) AS "auditors",
                        (
                      SELECT
                        STRING_AGG(department."name", ',')
                      FROM
                        (
                        SELECT
                          prd."departmentId"
                        FROM
                          planning_request_department prd
                        WHERE
                          prd."planningRequestId" = $1
                      ) AS tem
                        JOIN "department" department
                        ON
                        department.id = tem."departmentId"
                      ) AS "departmentName",
                      (
                      SELECT
                        JSON_AGG(tem)
                      FROM
                        (
                        SELECT
                          poc."serialNumber",
                          poc."comment",
                          poc."createdUser" :: json -> 'username' AS "commentedBy",
                          poc."createdUser" :: json -> 'jobTitle' AS "jobTitle",
                          poc."createdAt" AS "commentedDate"
                        FROM
                          pr_office_comment poc
                        WHERE
                          poc."planningRequestId" = $1) AS tem) AS "officeComments",
                      (
                      SELECT
                        JSON_AGG(tem)
                      FROM
                        (
                        SELECT
                          prh.status,
                          prh."createdUser"::json -> 'username' AS "updatedUser",
                          prh."createdUser" :: json -> 'jobTitle' AS "jobTitle",
                          prh."updatedAt",
                          prh."remark"
                        FROM
                          planning_request_history prh
                        WHERE
                          prh."planningRequestId" = $1) AS tem) AS "userHistorySession"
                    FROM
                      planning_request pr
                    LEFT JOIN vessel v
                                ON
                      v.id = pr."vesselId"
                  LEFT JOIN port_master fromport
                        ON
                      fromport.id = pr."fromPortId"
                  LEFT JOIN port_master toport
                        ON
                      toport.id = pr."toPortId"
                    LEFT  JOIN company c 
                    ON c.id = pr."companyId" 
                    WHERE
                      pr.id = $1
    `;

    return await this.query(rawQuery, [planingRequestId]);
  }

  async totalUnplannedPR(user: TokenPayloadModel) {
    const rawQuery = `
                      SELECT
                      count(DISTINCT pr.id ) 
                    FROM
                      planning_request pr
                    LEFT JOIN planning_request_auditor pra 
                    ON
                      pra."planningRequestId" = pr.id
                    WHERE
                      pra."userId" IS NULL AND pr."companyId" = $1
`;

    const data = await this.query(rawQuery, [user.companyId]);
    return data[0].count;
  }

  async getPRsByCarVerification(token: TokenPayloadModel, carId: string) {
    try {
      const qb = this.createQueryBuilder('planningRequest')
        .innerJoin('planningRequest.carVerificationPlannings', 'carVerificationPlannings')
        .innerJoin('carVerificationPlannings.carVerification', 'carVerification')
        .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
        .leftJoin('planningRequest.auditors', 'auditors')
        .leftJoin('planningRequest.fromPort', 'fromPort')
        .leftJoin('planningRequest.toPort', 'toPort')
        .where('carVerification.carId = :carId', { carId })
        .select([
          'planningRequest',
          'leadAuditor.username',
          'auditors.username',
          'fromPort.name',
          'toPort.name',
        ]);
      return await qb.getMany();
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] getPARListByNextVerificationCar error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async listInspectionForMapView(
    inspectionMapViewDto: ListInspectionMapViewQueryDTO,
    token: TokenPayloadModel,
  ) {
    try {
      const queryBuilder = this.createQueryBuilder('planningRequest')
        .leftJoin('planningRequest.location', 'location')
        .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
        .leftJoin('planningRequest.vessel', 'vessel')
        .leftJoin('vessel.vesselType', 'vesselType')
        .leftJoin('planningRequest.auditTypes', 'auditTypes')
        .leftJoin('planningRequest.auditors', 'auditors')
        .leftJoin('auditors.country', 'country')
        .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
        .leftJoin('planningRequest.auditCompany', 'auditCompany')
        //.leftJoin('planningRequest.department', 'department')
        .leftJoin('planningRequest.departments', 'departments')
        .leftJoin('planningRequest.fromPort', 'fromPort')
        .leftJoin('planningRequest.toPort', 'toPort')
        .leftJoin('planningRequest.company', 'company')
        .leftJoin('planningRequest.userAssignments', 'userAssignments')
        .leftJoin('userAssignments.user', 'user')
        .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
        .leftJoin('vesselDocHolders.company', 'companyDocHolder')
        .where('(planningRequest.companyId = :companyId )', {
          companyId: token.companyId,
        })
        .select();

      const fields = [
        'location.id',
        'location.name',
        'location.flagImg',
        'fromPort.id',
        'fromPort.name',
        'fromPort.latitude',
        'fromPort.longitude',
        'fromPort.code',
        'toPort.id',
        'toPort.name',
        'toPort.latitude',
        'toPort.longitude',
        'toPort.code',
        'vessel.id',
        'vessel.name',
        'vessel.image',
        'vesselType.name',
        'vesselType.icon',
        'auditTypes.id',
        'auditTypes.name',
        'auditors.id',
        'auditors.country',
        'auditors.countryId',
        'country.id',
        'country.name',
        'auditors.username',
        'leadAuditor.id',
        'leadAuditor.username',
        'auditCompany.name',
        'auditCompany.address',
        'auditCompany.logo',
        'auditCompany.geoLocation',
        'departments.id',
        'departments.name',
        'auditTimeTable.actualTo',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyDocHolder.name',
      ];
      queryBuilder.addSelect(fields).groupBy(
        `"planningRequest"."id", 
          "vesselType"."id", 
          "auditCompany"."id", 
          "auditTimeTable"."id", 
          "companyDocHolder"."id", 
          ${fields.join(', ')}`,
      );

      if (!RoleScopeCheck.isAdmin(token)) {
        const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
          this.manager,
          token.explicitCompanyId,
          'planningRequest',
        );
        queryBuilder
          // .leftJoin('planningRequest.userAssignments', 'userAssignments')
          // .leftJoin('userAssignments.user', 'user')
          .leftJoin('vessel.divisionMapping', 'divisionMapping')
          // .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
          // .leftJoin('vessel.vesselOwners', 'vesselOwners')
          .leftJoin('divisionMapping.division', 'division')
          .leftJoin('division.users', 'users')
          .leftJoin(
            'vessel.vesselOwners',
            'vesselOwners',
            // 'vesselOwners.fromDate < now() and COALESCE(vesselOwners.toDate,now()) >= now()',
          )
          .leftJoin(
            'vessel.vesselCharterers',
            'vesselCharterers',
            // 'vesselCharterers.fromDate < now() and COALESCE(vesselCharterers.toDate,now()) >= now()',
          );
        if (token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY) {
          queryBuilder.andWhere(
            `((planningRequest.entityType = :entityTypeVessel and ( users.id = :userId ` +
              whereForMainAndInternal +
              ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId)))',
            {
              userId: token.id,
              entityTypeVessel: AuditEntity.VESSEL,
              entityTypeOffice: AuditEntity.OFFICE,
              explicitCompanyId: token.explicitCompanyId,
            },
          );
        } else if (token.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
          queryBuilder.andWhere(
            `( users.id = :userId ` +
              whereForMainAndInternal +
              ' or planningRequest.entityType = :entityType)',
            {
              userId: token.id,
              entityType: AuditEntity.OFFICE,
            },
          );
        } else {
          queryBuilder.andWhere(
            `((planningRequest.entityType = :entityTypeVessel and ( auditors.id = :userId ` +
              whereForExternal +
              ' )) or (planningRequest.entityType = :entityTypeOffice and (auditors.id = :userId or user.id = :userId or planningRequest.auditCompanyId = :explicitCompanyId) ))',
            {
              userId: token.id,
              entityTypeVessel: AuditEntity.VESSEL,
              entityTypeOffice: AuditEntity.OFFICE,
              explicitCompanyId: token.explicitCompanyId,
            },
          );
        }
      }

      if (inspectionMapViewDto.entityTypes) {
        queryBuilder.andWhere('planningRequest.entityType IN (:...entityTypes) ', {
          entityTypes: inspectionMapViewDto.entityTypes,
        });
      }

      if (inspectionMapViewDto.childCompanyIds) {
        queryBuilder.andWhere('(planningRequest.auditCompanyId IN (:...childCompanyIds))', {
          childCompanyIds: inspectionMapViewDto.childCompanyIds,
        });
      }
      if (inspectionMapViewDto.portIds) {
        queryBuilder.andWhere('(fromPort.id IN (:...portIds) OR toPort.id IN (:...portIds))', {
          portIds: inspectionMapViewDto.portIds,
        });
      }
      if (inspectionMapViewDto.countryNames) {
        queryBuilder.andWhere('country.name IN (:...countryNames) ', {
          countryNames: inspectionMapViewDto.countryNames,
        });
      }
      if (inspectionMapViewDto.countryIds) {
        queryBuilder.andWhere('auditors.countryId IN (:...countryIds) ', {
          countryIds: inspectionMapViewDto.countryIds,
        });
      }

      switch (inspectionMapViewDto.planningType) {
        case PlanningType.ASSIGNED:
          queryBuilder.andWhere(`auditors.id IS NOT NULL`);
          break;
        case PlanningType.UNASSIGNED:
          queryBuilder.andWhere(`auditors.id IS NULL`);
          break;
        default:
      }

      if (inspectionMapViewDto.fromDate) {
        queryBuilder.andWhere('planningRequest.plannedToDate >= :fromDateParam', {
          fromDateParam: new Date(inspectionMapViewDto.fromDate),
        });
      }

      if (inspectionMapViewDto.toDate) {
        queryBuilder.andWhere('planningRequest.plannedFromDate <= :toDateParam', {
          toDateParam: new Date(inspectionMapViewDto.toDate),
        });
      }

      if (inspectionMapViewDto.auditorIds) {
        queryBuilder.andWhere(`auditors.id IN (:...auditorIds)`, {
          auditorIds: inspectionMapViewDto.auditorIds,
        });
      }

      if (inspectionMapViewDto.circleRange) {
        const centerPointStr = generatePointString(
          inspectionMapViewDto.circleRange.longitude,
          inspectionMapViewDto.circleRange.latitude,
        );
        // queryBuilder.addSelect([
        //   `ST_Distance("fromPort"."geoLocation", '${centerPointStr}'::geography) AS "fromPort_dist"`,
        //   `ST_Distance("toPort"."geoLocation", '${centerPointStr}'::geography) AS "toPort_dist"`,
        // ]);
        queryBuilder.andWhere(
          `(
            ST_DWithin("fromPort"."geoLocation", '${centerPointStr}'::geography, ${inspectionMapViewDto.circleRange.radius}) OR
            ST_DWithin("toPort"."geoLocation", '${centerPointStr}'::geography, ${inspectionMapViewDto.circleRange.radius})
          )`,
        );
      }

      // queryBuilder.addOrderBy('planningRequest.createdAt', 'DESC');

      // const items: any[] = await this.getRawManyQB(queryBuilder);

      // return {
      //   data: items,
      //   page: 1,
      //   pageSize: 0,
      //   totalPage: null,
      //   totalItem: items.length,
      // };

      return this.list(
        {
          page: inspectionMapViewDto.page,
          limit: inspectionMapViewDto.pageSize,
        },
        {
          queryBuilder,
          sort: inspectionMapViewDto.sort || 'planningRequest.createdAt:-1',
          advanceConditions: {
            createdAtFrom: inspectionMapViewDto.createdAtFrom,
            createdAtTo: inspectionMapViewDto.createdAtTo,
          },
        },
      );
    } catch (ex) {
      LoggerCommon.error(
        '[PlanningRequestRepository] listInspectionForMapView error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async listPRForMapView(token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .where('(planningRequest.companyId = :companyId)', {
        companyId: token.companyId,
      })
      .select()
      .addSelect(['auditors.id', 'auditors.username']);

    const planningRequests = await this.getManyQB(queryBuilder);
    return planningRequests;
  }

  async listInspectorMapView(body: ListInspectorMapViewBodyDTO, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.fromPort', 'fromPort')
      .leftJoin('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.company', 'company')
      .leftJoin('auditors.country', 'country')
      .where('(planningRequest.companyId = :companyId OR company.parentId = :companyId )', {
        companyId: token.companyId,
      })
      .select()
      .addSelect([
        'auditors.id',
        'auditors.countryId',
        'auditors.country',
        'country.id',
        'country.name',
        'auditors.username',
        'auditCompany.name',
        'fromPort.id',
        'fromPort.name',
        'fromPort.latitude',
        'fromPort.longitude',
        'fromPort.code',
        'toPort.id',
        'toPort.name',
        'toPort.latitude',
        'toPort.longitude',
        'toPort.code',
      ]);

    if (body.childCompanyIds) {
      queryBuilder.andWhere('(planningRequest.companyId IN (:...childCompanyIds))', {
        childCompanyIds: body.childCompanyIds,
      });
    }

    if (body.countryNames) {
      queryBuilder.andWhere('country.name IN (:...countryNames)', {
        countryNames: body.countryNames,
      });
    }

    if (body.fromDate) {
      queryBuilder.andWhere('planningRequest.plannedToDate >= :fromDateParam', {
        fromDateParam: new Date(body.fromDate),
      });
    }

    if (body.toDate) {
      queryBuilder.andWhere('planningRequest.plannedFromDate <= :toDateParam', {
        toDateParam: new Date(body.toDate),
      });
    }

    return await this.getManyQB(queryBuilder);
  }

  async pickedPlanningRequests(
    body: ListInspectorMapViewBodyDTO,
    selectedInspectorIds: string[],
    token: TokenPayloadModel,
  ) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('auditors.country', 'auditorCountry')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .leftJoin('leadAuditor.country', 'leadAuditorCountry')
      .leftJoinAndSelect('planningRequest.fromPort', 'fromPort')
      .leftJoinAndSelect('planningRequest.toPort', 'toPort')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.company', 'company')
      .where('(planningRequest.companyId = :companyId OR company.parentId = :companyId)', {
        companyId: token.companyId,
      })
      .select()
      .addSelect([
        'auditors.id',
        'auditors.countryId',
        'auditors.country',
        'auditorCountry.id',
        'auditorCountry.name',
        'auditors.username',
        'auditors.createdAt',
        'auditCompany.name',
        'auditCompany.geoLocation',
        'leadAuditor.id',
        'leadAuditor.countryId',
        'leadAuditor.country',
        'leadAuditorCountry.id',
        'leadAuditorCountry.name',
        'leadAuditor.username',
        'leadAuditor.createdAt',
      ]);

    if (body.childCompanyIds && body.childCompanyIds.length > 0) {
      queryBuilder.andWhere('(planningRequest.companyId IN (:...childCompanyIds))', {
        childCompanyIds: body.childCompanyIds,
      });
    }

    if (selectedInspectorIds.length > 0) {
      queryBuilder.andWhere('(auditors.id IN (:...auditors))', {
        auditors: selectedInspectorIds,
      });
    }

    if (body.fromDate && body.toDate) {
      const toDateParam = new Date(body.toDate);
      const fromDateParam = new Date(body.fromDate);
      if (body.searchAvailability === SearchAvailabilityEnum.UNAVAILABLE) {
        queryBuilder.andWhere(
          '(planningRequest.plannedFromDate >= :fromDateParam AND planningRequest.plannedToDate <= :toDateParam )',
          {
            toDateParam,
            fromDateParam,
          },
        );
      }

      if (body.searchAvailability === SearchAvailabilityEnum.PARTIALLY) {
        queryBuilder.andWhere(
          '((planningRequest.plannedFromDate <= :fromDateParam AND planningRequest.plannedToDate > :fromDateParam AND planningRequest.plannedToDate < :toDateParam) OR (planningRequest.plannedFromDate > :fromDateParam AND planningRequest.plannedToDate < :toDateParam) OR (planningRequest.plannedFromDate > :fromDateParam AND planningRequest.plannedFromDate < :toDateParam AND planningRequest.plannedToDate >= :toDateParam))',
          {
            toDateParam,
            fromDateParam,
          },
        );
      }
    }

    return await this.getManyQB(queryBuilder);
  }

  async planningRequestAuditors(user: TokenPayloadModel, body: PRAuditorsDto) {
    const params = [];
    const childCompanyIds = body.childCompanyIds || [];
    const companiesString = [...childCompanyIds, user.companyId].map((i) => {
      return `'${i}'`;
    });
    // const queryCompany = body?.allChildCompany
    //   ? `  (u."companyId" = '${user.companyId}' OR u."parentCompanyId" = '${user.companyId}')`
    //   : `u."companyId" IN (${companiesString})`;
    let rawQuery = `
      SELECT DISTINCT u.username, u.id
      FROM planning_request_auditor pra
      JOIN "user" u ON u.id = pra."userId"
      JOIN planning_request pr ON pr.id = pra."planningRequestId"
      left join user_role ur on ur."userId" = u.id
      left join role r on r.id = ur."roleId"
      where r.name = '${ROLE_NAME_FIXED.EXTERNAL_INSPECTOR}'
       AND u."companyId" = '${user?.explicitCompanyId}'`;

    if (body.portIds) {
      const portIdsString = body.portIds?.map((i) => {
        return `'${i}'`;
      });
      rawQuery =
        rawQuery +
        `AND
      (pr."fromPortId" IN (${portIdsString})
        OR pr."toPortId" IN (${portIdsString}))
      AND pr."companyId" IN (${companiesString})`;
    } else if (body.countryNames) {
      const countryNamesString = body.countryNames?.map((i) => {
        return `'${i}'`;
      });
      rawQuery =
        rawQuery +
        `AND
    u."country" IN (${countryNamesString})
    AND pr."companyId" IN (${companiesString})`;
    } else if (body.countryIds) {
      const countryIdsString = body.countryIds?.map((i) => {
        return `'${i}'`;
      });
      rawQuery =
        rawQuery +
        `AND
    u."countryId" IN (${countryIdsString})
    AND pr."companyId" IN (${companiesString})`;
    } else {
      rawQuery = rawQuery + `AND pr."companyId" IN (${companiesString})`;
    }

    if (body.forInspectorMapping) {
      rawQuery = rawQuery + `AND u."companyId" IN (${companiesString})`;
    }

    if (body.content) {
      rawQuery = rawQuery + `AND u.username ILIKE '%' || $1 || '%' `;
      params.push(`${body.content}`);
    }

    return this.connection.query(rawQuery, [...params]);
  }

  async checkUserCanUpdateRecord(userId: string, planningRequestId: string) {
    const userAssignments = await this.connection
      .getCustomRepository(UserAssignmentRepository)
      .find({
        where: {
          planningRequestId,
          userId,
        },
      });

    return userAssignments.length > 0 ? true : false;
  }

  async listUpcomingPlanningRequest(user: TokenPayloadModel, query: DateRangeDto) {
    const queryBuilder = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.vessel', 'vessel')
      .leftJoin('planningRequest.company', 'company')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
      .where(
        `planningRequest.companyId = :companyId AND planningRequest.deleted = false AND planningRequest.status = :status AND TO_CHAR("planningRequest"."plannedFromDate"::timestamp AT time zone 'PST', 'YYYY-MM-DD') >= TO_CHAR(NOW(), 'YYYY-MM-DD')`,
        {
          companyId: user.companyId,
          status: PlanningRequestStatus.APPROVED,
        },
      )
      .select(['planningRequest.id', 'planningRequest.refId', 'planningRequest.plannedFromDate'])
      .addSelect([
        'vessel.id',
        'vessel.code',
        'vessel.name',
        'auditCompany.id',
        'auditCompany.code',
        'auditCompany.name',
        'leadAuditor.id',
        'leadAuditor.username',
        'leadAuditor.firstName',
        'leadAuditor.lastName',
      ]);

    if (query.vesselId) {
      queryBuilder.andWhere('planningRequest.vesselId = :vesselId', {
        vesselId: query.vesselId,
      });
    }

    if (query.auditCompanyId) {
      queryBuilder.andWhere('planningRequest.auditCompanyId = :auditCompanyId', {
        auditCompanyId: query.auditCompanyId,
      });
    }

    if (query.fromDate) {
      queryBuilder.andWhere('planningRequest.plannedFromDate >= :fromDateParam', {
        fromDateParam: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere('planningRequest.plannedFromDate <= :toDateParam', {
        toDateParam: new Date(query.toDate),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('planningRequest.entityType = :entityType', {
        entityType: query.entityType,
      });
    }

    return await this.getManyQB(queryBuilder);
  }

  async checkRoleAndCompanyType(user: TokenPayloadModel) {
    const companyQuery = this.connection
      .getCustomRepository(CompanyRepository)
      .createQueryBuilder('company')
      .leftJoin('company.companyTypes', 'companyTypes')
      .andWhere(
        'company.id = :companyId and company.deleted = false and company.companyLevel = :companyLevel and companyTypes.companyType = :companyType',
        {
          companyId: user.explicitCompanyId,
          companyLevel: CompanyLevelEnum.EXTERNAL_COMPANY,
          companyType: COMPANY_TYPE_NAME_FIXED.INSPECTION_SERVICES,
        },
      );
    const rawQuery = `
              select
                ur."userId"
              from
                user_role ur
              left join role on
                ur."roleId" = role.id
              where
               ( role."companyId" = '${user.companyId}' OR role."companyId" IS NULL )
                and 
              role.name in ('${ROLE_NAME_FIXED.INSPECTOR}', '${ROLE_NAME_FIXED.INTERNAL_INSPECTOR}','${ROLE_NAME_FIXED.EXTERNAL_INSPECTOR}') 
                and ur."userId" = '${user.id}'
    `;
    const [company, listUserRole] = await Promise.all([
      companyQuery.getOne(),
      this.connection.query(rawQuery),
    ]);
    if (company && listUserRole && listUserRole.length > 0) {
      return true;
    }
    return false;
  }

  async _filterEntityTypeByRole(
    entityType,
    user: TokenPayloadModel,
    tableColId: string,
    alias: string,
    joinPlanningRequest?: boolean,
  ) {
    let filterEntityType = '';
    if (entityType) {
      filterEntityType = ` AND ${alias}."entityType" = '${entityType}'`;
    }
    let leftJoinNotAdmin = '';
    let conditionNotAdmin = '';
    if (!RoleScopeCheck.isAdmin(user)) {
      leftJoinNotAdmin = `
              left join vessel v on
	              v.id = pr."vesselId"
	            left join planning_request_auditor pra on
                pra."planningRequestId" = pr.id
              left join "user" "auditors" on
                "auditors".id = pra."userId"
              left join user_assignment ua on 
                ua."${tableColId}" = ${alias}.id
              left join "user" "user" on
                "user".id = ua."userId"
              left join vessel_doc_holder vdh  on
                v.id = vdh."vesselId" 
              left join vessel_charterer vc on
                v.ID = vc."vesselId"
              left join vessel_owner vo on
                v.ID = vo."vesselId"
              left join division_mapping dm on
                v.id = dm."vesselId"
              left join division d on
                dm."divisionId" = d.id
              left join division_user du on
                du."divisionId" = d.id
              left join "user" "users" on
                du."userId" = "users".id       
      `;
      if (joinPlanningRequest) {
        leftJoinNotAdmin =
          `
                left join planning_request pr on
                ${alias}."planningRequestId" = pr.id` + leftJoinNotAdmin;
      }
      if (
        user.companyLevel === CompanyLevelEnum.EXTERNAL_COMPANY &&
        (await this.checkRoleAndCompanyType(user))
      ) {
        conditionNotAdmin = ` AND((pr."entityType" = '${AuditEntity.VESSEL}' 
                              AND (pra."userId" = '${user.id}' OR ua."userId" = '${user.id}')) OR (pr."entityType" = '${AuditEntity.OFFICE}' 
                              AND (pra."userId" = '${user.id}' OR ua."userId" = '${user.id}' 
                                OR pr."auditCompanyId" = '${user.explicitCompanyId}')))`;
      } else {
        conditionNotAdmin = joinPlanningRequest
          ? await this.getConditionForUserNotAdmin(user, alias === 'iar' ? 'pr' : alias)
          : await this.getConditionForUserNotAdmin(user);
      }
    }
    return { leftJoinNotAdmin, filterEntityType, conditionNotAdmin };
  }

  async _updateGlobalStatus(manager, checkIsDup: boolean) {
    const prNeedUpdate: PlanningRequest[] = await this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.internalAuditReport', 'iar')
      .where('iar.status = :status', { status: InternalAuditReportStatus.APPROVED })
      .select(['planningRequest.id', 'planningRequest.status', 'planningRequest.globalStatus'])
      .addSelect(['iar.id', 'iar.status'])
      .getMany();

    for (const pr of prNeedUpdate) {
      const foundCars = await manager
        .getCustomRepository(CARRepository)
        .createQueryBuilder('car')
        .leftJoin('car.cap', 'cap')
        .where(
          'car.planningRequestId = :planningRequestId' + checkIsDup
            ? ' and car.isDup = FALSE '
            : '',
          {
            planningRequestId: pr.id,
          },
        )
        .select(['car.id', 'car.isDup'])
        .addSelect(['cap.id', 'cap.status'])
        .orderBy('cap.createdAt', 'DESC')
        .getMany();

      const checkHasCar = checkIsDup ? foundCars.length > 0 : foundCars.every((item) => item.isDup);
      const checkCarHasCap = foundCars.some((item) => item.cap);
      const checkCarHasCapSubmitted = foundCars.some(
        (item) => item.cap?.status === CapStatusEnum.SUBMITTED,
      );
      const checkCarHasCapDenied = foundCars.some(
        (item) => item.cap?.status === CapStatusEnum.DENIED,
      );

      const verifiedCar = await manager
        .getCustomRepository(CARRepository)
        .createQueryBuilder('car')
        .innerJoinAndSelect('car.cARVerification', 'verification')
        .where('car.planningRequestId = :planningRequestId', {
          planningRequestId: pr.id,
        })
        .orderBy('verification.updatedAt', 'DESC')
        .getOne();

      await this.heplerSwitch(
        checkHasCar,
        checkCarHasCap,
        checkCarHasCapSubmitted,
        checkCarHasCapDenied,
        verifiedCar,
        pr,
        manager,
      );
    }
  }

  heplerSwitch(
    checkHasCar: boolean,
    checkCarHasCap: boolean,
    checkCarHasCapSubmitted: boolean,
    checkCarHasCapDenied: boolean,
    verifiedCar: any,
    pr: any,
    manager: any,
  ) {
    if (!checkHasCar && !verifiedCar) {
      return manager.update(
        PlanningRequest,
        {
          id: pr.id,
        },
        {
          globalStatus: GlobalStatusEnum.APPROVED_REPORT,
        },
      );
    } else if (checkHasCar && !verifiedCar) {
      if (!(checkCarHasCapSubmitted || checkCarHasCapDenied)) {
        return manager.update(
          PlanningRequest,
          {
            id: pr.id,
          },
          {
            globalStatus: GlobalStatusEnum.SENT_CAR_UNDER_CAP_PREPARATION,
          },
        );
      }
      if (checkCarHasCapSubmitted) {
        return manager.update(
          PlanningRequest,
          {
            id: pr.id,
          },
          {
            globalStatus: GlobalStatusEnum.SUBMIT_CAP,
          },
        );
      } else if (checkCarHasCapDenied) {
        return manager.update(
          PlanningRequest,
          {
            id: pr.id,
          },
          {
            globalStatus: GlobalStatusEnum.DISAPPROVED_CAP,
          },
        );
      }
    } else if (verifiedCar) {
      const carVerification = verifiedCar.cARVerification;
      if (!carVerification.isNeeded) {
        return manager.update(
          PlanningRequest,
          {
            id: pr.id,
          },
          {
            globalStatus: GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
          },
        );
      } else {
        if (
          carVerification.status === CarVerificationStatusEnum.PENDING ||
          carVerification.status === CarVerificationStatusEnum.HOLDING
        ) {
          return manager.update(
            PlanningRequest,
            {
              id: pr.id,
            },
            {
              globalStatus: GlobalStatusEnum.WAITING_VERIFICATION,
            },
          );
        } else if (
          carVerification.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE ||
          carVerification.status === CarVerificationStatusEnum.OVERRIDING_CLOSURE
        ) {
          return manager.update(
            PlanningRequest,
            {
              id: pr.id,
            },
            {
              globalStatus: GlobalStatusEnum.APPROVED_VERIFICATION,
            },
          );
        }
      }
    }
  }

  async _migrateGlobalStatusComplex() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_GLOBAL_STATUS_COMPLEX,
      })
      .getOne();

    if (!metaConfig) {
      return await this.connection.transaction(async (manager) => {
        await this._updateGlobalStatus(manager, false);

        await manager.save(MetaConfig, {
          key: CatalogConst.MIGRATE_GLOBAL_STATUS_COMPLEX,
          lastTimeSync: '2023-08-04T04:20:00.000z',
        });
        console.log('Migrate done');
      });
    }

    return 1;
  }

  _getPreviousReassignStatus(
    prHistories: PlanningRequestHistory[],
    userAssignment?: UserAssignment[],
  ) {
    if (prHistories[0]?.status !== PlanningRequestStatus.REJECTED) {
      return prHistories[0].status;
    }
    let countReject = 1;
    let lastReviewStatus;
    for (let i = 1; i < prHistories.length; i++) {
      if (prHistories[i].status === PlanningRequestStatus.REJECTED) {
        countReject += 1;
      } else {
        lastReviewStatus =
          prHistories[i].status === PlanningRequestStatus.DRAFT
            ? PlanningRequestStatus.SUBMITTED
            : prHistories[i].status;
        break;
      }
    }
    if (countReject === 2) {
      return lastReviewStatus;
    }
    switch (lastReviewStatus) {
      case PlanningRequestStatus.REVIEWED_5:
        switch (countReject) {
          case 1:
            return PlanningRequestStatus.APPROVED;
          case 3:
            return PlanningRequestStatus.REVIEWED_4;
          case 4:
            return PlanningRequestStatus.REVIEWED_3;
          case 5:
            return PlanningRequestStatus.REVIEWED_2;
          case 6:
            return PlanningRequestStatus.REVIEWED_1;
        }
        break;
      case PlanningRequestStatus.REVIEWED_4:
        switch (countReject) {
          case 1:
            return userAssignment?.length
              ? PlanningRequestStatus.REVIEWED_5
              : PlanningRequestStatus.APPROVED;
          case 3:
            return PlanningRequestStatus.REVIEWED_3;
          case 4:
            return PlanningRequestStatus.REVIEWED_2;
          case 5:
            return PlanningRequestStatus.REVIEWED_1;
        }
        break;
      case PlanningRequestStatus.REVIEWED_3:
        switch (countReject) {
          case 1:
            return userAssignment?.length
              ? PlanningRequestStatus.REVIEWED_4
              : PlanningRequestStatus.APPROVED;
          case 3:
            return PlanningRequestStatus.REVIEWED_2;
          case 4:
            return PlanningRequestStatus.REVIEWED_1;
        }
        break;
      case PlanningRequestStatus.REVIEWED_2:
        switch (countReject) {
          case 1:
            return userAssignment?.length
              ? PlanningRequestStatus.REVIEWED_3
              : PlanningRequestStatus.APPROVED;
          case 3:
            return PlanningRequestStatus.REVIEWED_1;
        }
        break;
      case PlanningRequestStatus.REVIEWED_1:
        if (countReject === 1) {
          return userAssignment?.length
            ? PlanningRequestStatus.REVIEWED_2
            : PlanningRequestStatus.APPROVED;
        }
        break;
      case PlanningRequestStatus.SUBMITTED:
        return PlanningRequestStatus.REVIEWED_1;
    }
  }
  _getListUpdateUAIds(userAssignments: UserAssignment[], reassignFrom: PlanningRequestStatus) {
    switch (reassignFrom) {
      case PlanningRequestStatus.APPROVED:
        return userAssignments
          .filter((user) => user.permission === WorkflowPermission.APPROVER)
          .map((user) => user.id);
      case PlanningRequestStatus.REVIEWED_5:
        return userAssignments
          .filter((user) => user.permission === WorkflowPermission.REVIEWER5)
          .map((user) => user.id);
      case PlanningRequestStatus.REVIEWED_4:
        return userAssignments
          .filter((user) => user.permission === WorkflowPermission.REVIEWER4)
          .map((user) => user.id);
      case PlanningRequestStatus.REVIEWED_3:
        return userAssignments
          .filter((user) => user.permission === WorkflowPermission.REVIEWER3)
          .map((user) => user.id);
      case PlanningRequestStatus.REVIEWED_2:
        return userAssignments
          .filter((user) => user.permission === WorkflowPermission.REVIEWER2)
          .map((user) => user.id);
      case PlanningRequestStatus.REVIEWED_1:
        return userAssignments
          .filter((user) => user.permission === WorkflowPermission.REVIEWER1)
          .map((user) => user.id);
    }
  }

  _getNextUserReview(userAssignments: UserAssignment[], status: PlanningRequestStatus) {
    switch (status) {
      case PlanningRequestStatus.SUBMITTED:
        return userAssignments?.filter((user) => user.permission === WorkflowPermission.REVIEWER1);
      case PlanningRequestStatus.REVIEWED_1:
        return userAssignments?.filter((user) => user.permission === WorkflowPermission.REVIEWER2);
      case PlanningRequestStatus.REVIEWED_2:
        return userAssignments?.filter((user) => user.permission === WorkflowPermission.REVIEWER3);
      case PlanningRequestStatus.REVIEWED_3:
        return userAssignments?.filter((user) => user.permission === WorkflowPermission.REVIEWER4);
      case PlanningRequestStatus.REVIEWED_4:
        return userAssignments?.filter((user) => user.permission === WorkflowPermission.REVIEWER5);
      case PlanningRequestStatus.REVIEWED_5:
        return userAssignments?.filter((user) => user.permission === WorkflowPermission.APPROVER);
    }
  }

  _comparePermissionAndStatus(status: PlanningRequestStatus) {
    switch (status) {
      case PlanningRequestStatus.SUBMITTED:
        return WorkflowPermission.CREATOR;
      case PlanningRequestStatus.REVIEWED_1:
        return WorkflowPermission.REVIEWER1;
      case PlanningRequestStatus.REVIEWED_2:
        return WorkflowPermission.REVIEWER2;
      case PlanningRequestStatus.REVIEWED_3:
        return WorkflowPermission.REVIEWER3;
      case PlanningRequestStatus.REVIEWED_4:
        return WorkflowPermission.REVIEWER4;
      case PlanningRequestStatus.REVIEWED_5:
        return WorkflowPermission.REVIEWER5;
    }
  }
  _getListReviewUAIds(userAssignments: UserPermissionDTO[], currentStatus: PlanningRequestStatus) {
    let userPermission: UserPermissionDTO;
    switch (currentStatus) {
      case PlanningRequestStatus.SUBMITTED:
        userPermission = userAssignments.filter(
          (x) => x.permission === WorkflowPermission.REVIEWER1,
        )[0];
        break;
      case PlanningRequestStatus.REVIEWED_1:
        userPermission = userAssignments.filter(
          (x) => x.permission === WorkflowPermission.REVIEWER2,
        )[0];
        break;
      case PlanningRequestStatus.REVIEWED_2:
        userPermission = userAssignments.filter(
          (x) => x.permission === WorkflowPermission.REVIEWER3,
        )[0];
        break;
      case PlanningRequestStatus.REVIEWED_3:
        userPermission = userAssignments.filter(
          (x) => x.permission === WorkflowPermission.REVIEWER4,
        )[0];
        break;
      case PlanningRequestStatus.REVIEWED_4:
        userPermission = userAssignments.filter(
          (x) => x.permission === WorkflowPermission.REVIEWER5,
        )[0];
        break;
      case PlanningRequestStatus.REVIEWED_5:
        userPermission = userAssignments.filter(
          (x) => x.permission === WorkflowPermission.APPROVER,
        )[0];
        break;
    }
    return userPermission.userIds.length
      ? userPermission
      : userAssignments.filter((x) => x.permission === WorkflowPermission.APPROVER)[0];
  }
  _checkUserHaveFullPermission(permission: WorkflowPermission) {
    return (
      WorkflowPermission.CREATOR === permission ||
      WorkflowPermission.REVIEWER1 === permission ||
      WorkflowPermission.REVIEWER2 === permission ||
      WorkflowPermission.REVIEWER3 === permission ||
      WorkflowPermission.REVIEWER4 === permission ||
      WorkflowPermission.REVIEWER5 === permission ||
      WorkflowPermission.APPROVER === permission
    );
  }
  async getDetailPRForUpdate(id: string, token: TokenPayloadModel) {
    const prDetail = await this.createQueryBuilder('planningRequest')
      .leftJoinAndSelect('planningRequest.pRFocusRequests', 'pRFocusRequests')
      .leftJoinAndSelect('pRFocusRequests.focusRequest', 'focusRequest')
      .leftJoinAndSelect('planningRequest.auditTypes', 'auditTypes')
      .where('planningRequest.id = :id', {
        id,
      })
      .andWhere('planningRequest.deleted = :deleted AND (planningRequest.companyId = :companyId)', {
        deleted: false,
        companyId: token.companyId,
      })
      .select()
      .getOne();
    return prDetail;
  }

  async _checkIsDirectFeature(
    userAssignments,
    workflows,
    token: TokenPayloadModel,
    status: string,
    previousStatus: string,
    planningRequest: PlanningRequest,
  ) {
    const currentReviewers = [];
    const allReviwer = [];
    const userAssignmentArr = [];
    const workflowPermission = {};
    const userAssignmentPermission = {};
    workflows.forEach((item) => {
      if (item?.permission != WorkflowPermission.CREATOR) {
        const { permission } = item;
        if (!(permission in workflowPermission)) {
          workflowPermission[item?.permission] = [];
        }
        workflowPermission[item?.permission].push(item);
      }
    });
    userAssignments.forEach((item) => {
      if (item?.permission != WorkflowPermission.CREATOR) {
        const { permission } = item;
        if (!(permission in userAssignmentPermission)) {
          userAssignmentPermission[item?.permission] = [];
        }
        userAssignmentPermission[item?.permission].push(item);
      }
    });

    const customComparator = (a, b) => {
      // Extract the permission values from the first elements of each sub-array
      const permissionA = a[0]?.permission || '';
      const permissionB = b[0]?.permission || '';

      // If both permissions are 'approver', preserve the original order
      if (
        permissionA === WorkflowPermission.APPROVER &&
        permissionB === WorkflowPermission.APPROVER
      )
        return 0;
      // If only permissionA is 'approver', push it to the end
      if (permissionA === WorkflowPermission.APPROVER) return 1;
      // If only permissionB is 'approver', keep it in the current position
      if (permissionB === WorkflowPermission.APPROVER) return -1;

      // Sort other permissions alphabetically
      if (permissionA < permissionB) return -1;
      if (permissionA > permissionB) return 1;
      return 0;
    };

    const workflowArr = Object.values(workflowPermission);
    userAssignmentArr.push(...Object.values(userAssignmentPermission));
    // here sorting the userAssignmentArray
    const sortedUserAssignments = userAssignmentArr?.sort(customComparator);
    const userAssignmentArr1 = [...userAssignmentArr];
    let lastRevievedStep: number;
    if (status?.includes('reviewed_')) {
      lastRevievedStep = Number(status?.split('reviewed_')[1]) || 0; // here remove the last reviewed step
      sortedUserAssignments?.splice(0, lastRevievedStep);
    } else if (previousStatus === PlanningRequestStatus.REJECTED) {
      sortedUserAssignments?.splice(0, Number(status?.split('reviewed_')[1])); // here remove the last reviewed step
    }
    sortedUserAssignments?.forEach((reviewers) => {
      reviewers.forEach((item) => {
        const { permission, roleId } = item ?? {};

        if (
          (permission?.startsWith(WorkflowPermission.REVIEWER) ||
            permission === WorkflowPermission.APPROVER) &&
          token.id === item?.userId &&
          status !== PlanningRequestStatus.APPROVED
        ) {
          currentReviewers?.push(permission);
        }

        if (
          permission?.startsWith(WorkflowPermission.REVIEWER) ||
          permission === WorkflowPermission.APPROVER
        ) {
          allReviwer?.push(permission);
        }
      });
    });
    const uniqueCurrentReviewer = [...new Set(currentReviewers)];
    const uniqueAllReviwer = [...new Set(allReviwer)];

    const firstReviewer = Number(uniqueCurrentReviewer[0]?.split(WorkflowPermission.REVIEWER)[1]); // here get the first reviewerStep from uniqueCurrentReviewer for checking seqeuence reviewer or not
    let currentReviewerStep = 0;
    if (uniqueCurrentReviewer?.includes(WorkflowPermission.APPROVER)) {
      uniqueCurrentReviewer?.slice(0, -1)?.map((item, index) => {
        if (uniqueCurrentReviewer[index] === `reviewer${firstReviewer + index}`) {
          currentReviewerStep = firstReviewer + index;
        }
      });
    } else {
      uniqueCurrentReviewer?.map((item, index) => {
        if (uniqueCurrentReviewer[index] === `reviewer${firstReviewer + index}`) {
          currentReviewerStep = firstReviewer + index;
        }
      });
    }
    if (PlanningRequestStatus.REJECTED === status) {
      if (planningRequest?.reassignFrom === PlanningRequestStatus.APPROVED) {
        currentReviewerStep = Number(previousStatus.split('_')[1]);
      } else {
        currentReviewerStep = Number(planningRequest?.reassignFrom?.split('_')[1]) - 1;
      }
      userAssignmentArr1?.splice(currentReviewerStep);
    }

    if (workflowArr?.length === userAssignmentArr1?.length) {
      if (uniqueCurrentReviewer?.length === uniqueAllReviwer?.length) {
        return { isDirectApproved: true, currentReviewerStep };
      } else {
        if (status === PlanningRequestStatus.SUBMITTED && currentReviewerStep > 0) {
          return {
            isDirectApproved: false,
            currentReviewerStep,
            isReviewEnabled: true,
          };
        } else {
          return {
            isDirectApproved: false,
            currentReviewerStep,
            isReviewEnabled:
              currentReviewerStep === 0
                ? false
                : uniqueAllReviwer?.includes(`reviewer${status?.split('reviewed_')[1]}`)
                ? false
                : true,
          };
        }
      }
    } else {
      return {
        isReviewEnabled:
          currentReviewerStep === 0
            ? false
            : uniqueAllReviwer?.includes(`reviewer${status?.split('reviewed_')[1]}`)
            ? false
            : true,
        currentReviewerStep,
      };
    }
  }

  /**
   * Check for existing planning requests within a 3-month range
   * @param auditTypeIds - Array of audit type IDs to check
   * @param plannedFromDate - Start date of the planning period
   * @param plannedToDate - End date of the planning period
   * @param vesselId - Optional vessel ID to filter by
   * @param auditCompanyId - Optional audit company ID to filter by
   * @returns Array of planning requests that match the criteria
   */
  async checkExistingPlan(
    auditTypeIds: string[],
    plannedFromDate: string,
    vesselId?: string,
    auditCompanyId?: string,
  ) {
    // Build base query with joins and conditions
    const baseQuery = this.createQueryBuilder('planningRequest')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .select([
        'planningRequest.id',
        'planningRequest.auditNo',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.status',
      ])
      // Filter by either vessel ID or audit company ID
      .where(
        vesselId
          ? 'planningRequest.vesselId = :vesselId'
          : 'planningRequest.auditCompanyId = :auditCompanyId',
        vesselId ? { vesselId } : { auditCompanyId },
      )
      // Check if planned dates are within 3 months before OR after the given date
      .andWhere(
        `(planningRequest.plannedFromDate BETWEEN (:plannedFromDate::TIMESTAMP - INTERVAL '3 MONTHS') AND (:plannedFromDate::TIMESTAMP + INTERVAL '3 MONTHS')
          OR planningRequest.plannedToDate BETWEEN (:plannedFromDate::TIMESTAMP - INTERVAL '3 MONTHS') AND (:plannedFromDate::TIMESTAMP + INTERVAL '3 MONTHS'))`,
        {
          plannedFromDate,
        },
      )
      // Only include non-deleted requests
      .andWhere('planningRequest.deleted = false')
      // Filter by audit types
      .andWhere('auditTypes.id = ANY(:auditTypeIds)', { auditTypeIds })
      // Exclude cancelled requests
      .andWhere('planningRequest.status != :status AND planningRequest.deleted = :deleted', {
        status: PlanningRequestStatus.CANCELLED,
        deleted: false,
      });

    return baseQuery.getMany();
  }
}
