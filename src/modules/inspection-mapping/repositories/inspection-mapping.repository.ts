import {
  <PERSON><PERSON>rror,
  BaseMultiErrors,
  CommonStatus,
  LoggerCommon,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository, In } from 'typeorm';

import {
  ActionEnum,
  AuditChkStatus,
  AuditWorkspaceStatus,
  PlanningRequestStatus,
} from '../../../commons/enums';
import { AuditChecklist } from '../../audit-checklist/entity/audit-checklist.entity';
import { AuthorityMaster } from '../../authority-master/authority-master.entity';
import { InspMapNatFinding } from '../entities/insp-map-nat-finding.entity';
import {
  CreateInspectionMappingDto,
  ListInspectionMappingDto,
  UpdateInspectionMappingDto,
} from '../dto';
import { InspectionMapping } from '../entities/inspection-mapping.entity';
import { AuditType } from '../../audit-type/audit-type.entity';
import { DBErrorCode } from '../../../commons/consts/db.const';
import { UserRepository } from '../../user/user.repository';
import { InspectionMappingHistory } from '../entities/inspection-mapping-history.entity';
import { FillAuditChecklist } from '../../audit-workspace/entities/fill-audit-checklist.entity';
import { MySet } from '../../../utils';
import { AuditWorkspace } from '../../audit-workspace/entities/audit-workspace.entity';
import { AuditChecklistRepository } from '../../audit-checklist/repository/audit-checklist.repository';
import { AuditWorkspaceRepository } from '../../audit-workspace/repositories/audit-workspace.repository';
import { NatureFindingRepository } from 'src/modules/nature_finding/nature-finding.repository';
import { SelfAssessment } from '../../../modules-qa/self-assessment/entity/self-assessment.entity';
import { PlanningRequest } from '../../planning-request/entities/planning-request.entity';
import { StandardMaster } from '../../../modules-qa/standard-master/entity/standard-master.entity';

@EntityRepository(InspectionMapping)
export class InspectionMappingRepository extends TypeORMRepository<InspectionMapping> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async createInspectionMapping(entityParams: CreateInspectionMappingDto, user: TokenPayloadModel) {
    try {
      const {
        auditChecklistIds = [],
        natureFindingIds,
        primaryFinding,
        authorityIds,
        auditTypeId,
        smChecklistIds = [],
        isSA,
        ...inspectionMappingObj
      } = entityParams;
      const inspectionMappingId = Utils.strings.generateUUID();
      const primaryFindingList = [];
      //Check active nature finding
      await this.manager
        .getCustomRepository(NatureFindingRepository)
        ._checkActiveNautureFinding(natureFindingIds, user);
      for (let i = 0; i < natureFindingIds.length; i++) {
        const natureFindingId = natureFindingIds[i];
        primaryFindingList.push({
          id: Utils.strings.generateUUID(),
          natureFindingId,
          inspectionMappingId,
          isPrimaryFinding: primaryFinding === natureFindingId,
        });
      }

      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);

      const rs = await this.connection.transaction(async (manager) => {
        let auditChecklists = [];
        if (auditChecklistIds?.length > 0 && entityParams.isSA == false) {
          auditChecklists = await manager.find(AuditChecklist, {
            where: {
              id: In(auditChecklistIds),
              deleted: false,
              companyId: user.companyId,
              status: AuditChkStatus.APPROVED,
            },
            select: ['id'],
          });
          if (auditChecklists.length !== auditChecklistIds.length) {
            throw new BaseError({ message: 'auditChecklist.INVALID' });
          }
        }

        // Handle SA checklists if needed
        let smChecklists = [];
        if (smChecklistIds?.length > 0 && entityParams?.isSA == true) {
          // If saChecklistIds is used, add appropriate validation here
          // This is placeholder code that should be adjusted based on your requirements
          smChecklists = await manager.find(StandardMaster, {
            where: {
              id: In(smChecklistIds),
              deleted: false,
              companyId: user.companyId,
            },
            select: ['id'],
          });
        }

        const authorityLists = await manager.find(AuthorityMaster, {
          where: { id: In(authorityIds), deleted: false, companyId: user.companyId },
          select: ['id'],
        });

        const auditType = await manager.findOne(AuditType, {
          where: { id: auditTypeId, deleted: false, companyId: user.companyId },
        });

        // if (auditChecklists.length !== auditChecklistIds.length) {
        //   throw new BaseError({ message: 'auditChecklist.INVALID' });
        // }

        if (authorityLists.length !== authorityIds.length) {
          throw new BaseError({ message: 'authority.INVALID' });
        }

        if (!auditType) {
          throw new BaseError({ message: 'auditType.INVALID' });
        }

        await manager.save(InspectionMapping, {
          ...inspectionMappingObj,
          id: inspectionMappingId,
          companyId: user.companyId,
          createdUserId: user.id,
          auditChecklists: auditChecklists as AuditChecklist[],
          standardMasters: smChecklists as StandardMaster[],
          authorities: authorityLists as AuthorityMaster[],
          auditTypeId: auditTypeId,
          isSA: isSA,
        });

        await manager.insert(InspMapNatFinding, primaryFindingList);

        await manager.insert(InspectionMappingHistory, {
          inspectionMappingId,
          status: ActionEnum.CREATE,
          createdUser,
        });

        if (auditChecklistIds.length > 0) {
          // List all new audit workspaces depend on this inspection mapping
          let auditWorkspacesNeedUpdate = await manager
            .createQueryBuilder(AuditWorkspace, 'auditWorkspace')
            .leftJoinAndSelect('auditWorkspace.planningRequest', 'planningRequest')
            .leftJoinAndSelect('planningRequest.auditTypes', 'auditTypes')
            .where(
              `auditWorkspace.status = :status 
              AND auditWorkspace.companyId = :companyId`,
              {
                status: AuditWorkspaceStatus.NEW,
                companyId: user.companyId,
                auditTypeId: auditTypeId,
              },
            )
            .select([
              'auditWorkspace.id',
              'auditWorkspace.planningRequestId',
              'auditWorkspace.inspectionMappingIds',
              'planningRequest',
              'auditTypes',
            ])
            .getMany();

          auditWorkspacesNeedUpdate = auditWorkspacesNeedUpdate?.filter((item) => {
            return item.planningRequest?.auditTypes?.some(
              (auditType) => auditType.id === auditTypeId,
            );
          });

          for (const auditWorkspaceNeedUpdate of auditWorkspacesNeedUpdate) {
            await manager.save(AuditWorkspace, {
              ...auditWorkspaceNeedUpdate,
              inspectionMappingIds: [
                inspectionMappingId,
                ...auditWorkspaceNeedUpdate.inspectionMappingIds,
              ],
            });
          } 

          if (auditWorkspacesNeedUpdate.length > 0) {
            // Loop all needed update audit workspaces
            const newInsAuditChecklistRepo = manager.getCustomRepository(AuditChecklistRepository);
            const newInsAuditWorkspaceRepo = manager.getCustomRepository(AuditWorkspaceRepository);
            for (let t = 0; t < auditWorkspacesNeedUpdate.length; t++) {
              const validAuditChecklists = await newInsAuditChecklistRepo._listValidAuditCheckListsByPR(
                auditWorkspacesNeedUpdate[t].planningRequestId,
                inspectionMappingId,
                auditChecklistIds,
                [
                  'id',
                  'question.id',
                  'question.locationId',
                  'inspectionMapping.id',
                  'auditType.id',
                ],
              );

              if (validAuditChecklists.length > 0) {
                await newInsAuditWorkspaceRepo._initFillAuditChecklistAndQuestions(
                  manager,
                  auditWorkspacesNeedUpdate[t].id,
                  validAuditChecklists,
                );
              }
            }
          }
        }
      });
      return rs;
    } catch (ex) {
      LoggerCommon.error(
        '[InspectionMappingRepository] createInspectionMapping error ',
        ex.message || ex,
      );
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw new BaseMultiErrors({
          status: 400,
          errors: [{ fieldName: 'auditTypeId', message: 'inspectionMapping.AUDIT_TYPE_EXISTED' }],
        });
      }
      throw ex;
    }
  }

  async listInspectionMapping(query: ListInspectionMappingDto, user: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('inspectionMapping')
      .leftJoin('inspectionMapping.createdUser', 'createdUser')
      .leftJoin('inspectionMapping.updatedUser', 'updatedUser')
      .leftJoin('inspectionMapping.auditType', 'auditType')
      .leftJoin('inspectionMapping.natureFindings', 'inspMapNatFinding')
      .leftJoin('inspMapNatFinding.natureFinding', 'natureFinding')
      .innerJoin('inspectionMapping.company', 'company')
      .where(
        '((inspectionMapping.companyId = :companyId OR company.parentId = :companyId) AND inspectionMapping.deleted IS FALSE AND inspMapNatFinding.isPrimaryFinding IS TRUE)',
        {
          companyId: user.companyId,
        },
      )
      .select()
      .addSelect([
        'createdUser.username',
        'updatedUser.username',
        'inspMapNatFinding.id',
        'inspMapNatFinding.isPrimaryFinding',
        'natureFinding.id',
        'natureFinding.name',
        'auditType.name',
        'company.id',
        'company.name',
      ]);
    if (query.content) {
      queryBuilder.andWhere(
        '(inspectionMapping.auditPeriod = :auditPeriod OR auditType.name LIKE :content)',
        {
          scope: `%${query.content}%`,
          auditPeriod: Number(query.content) || -1,
          content: `%${query.content}%`,
        },
      );
    }
    if (query.status) {
      queryBuilder.andWhere('inspectionMapping.status = :status', {
        status: query.status,
      });
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'inspectionMapping.createdAt:-1',
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );
  }

  async detailInspectionMapping(inspectionMappingId: string, user: TokenPayloadModel) {
    const inspectionMapping = await this.getOneQB(
      this.createQueryBuilder('inspectionMapping')
        .leftJoinAndSelect('inspectionMapping.statusHistory', 'statusHistory')
        .leftJoin('inspectionMapping.createdUser', 'createdUser')
        .leftJoin('inspectionMapping.updatedUser', 'updatedUser')
        .leftJoinAndSelect('inspectionMapping.auditChecklists', 'auditChecklists')
        .leftJoinAndSelect('inspectionMapping.authorities', 'authorities')
        .leftJoinAndSelect('inspectionMapping.auditType', 'auditType')
        .leftJoinAndSelect('inspectionMapping.natureFindings', 'inspMapNatFinding')
        .leftJoinAndSelect('inspMapNatFinding.natureFinding', 'natureFinding')
        .leftJoinAndSelect('inspectionMapping.standardMasters', 'standardMasters')
        .select()
        .addSelect(['createdUser.username', 'updatedUser.username'])
        .where('(inspectionMapping.id = :id AND inspectionMapping.deleted = :deleted )', {
          id: inspectionMappingId,
          deleted: false,
        }),
    );

    if (inspectionMapping) {
      return inspectionMapping;
    } else {
      throw new BaseError({ status: 404, message: 'inspectionMapping.NOT_FOUND' });
    }
  }

  async updatedInspectionMapping(
    inspectionMappingId: string,
    entityParams: UpdateInspectionMappingDto,
    user: TokenPayloadModel,
  ) {
    try {
      const inspectionFound = await this.detailInspectionMapping(inspectionMappingId, user);
      if (!inspectionFound) {
        throw new BaseError({ status: 404, message: 'inspectionMapping.NOT_FOUND' });
      }
      const {
        auditChecklistIds = [],
        natureFindingIds,
        primaryFinding,
        authorityIds,
        auditTypeId,
        smChecklistIds = [],
        isSA,
        ...inspectionMappingObj
      } = entityParams;

      const currentNFIds = inspectionFound.natureFindings?.map((item) => item.natureFinding.id);
      const newNFIds = natureFindingIds.filter((id) => !currentNFIds.includes(id));
      if (newNFIds.length) {
        await this.manager
          .getCustomRepository(NatureFindingRepository)
          ._checkActiveNautureFinding(newNFIds, user);
      }
      const primaryFindingList = [];
      for (let i = 0; i < natureFindingIds.length; i++) {
        const natureFindingId = natureFindingIds[i];
        primaryFindingList.push({
          natureFindingId,
          inspectionMappingId,
          isPrimaryFinding: primaryFinding === natureFindingId,
        });
      }

      // Get user info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);

      const rs = await this.connection.transaction(async (manager) => {
        // Handle audit checklists only if auditChecklistIds are provided
        let auditChecklists = [];
        if (auditChecklistIds?.length > 0 && entityParams.isSA == false) {
          auditChecklists = await manager.find(AuditChecklist, {
            where: {
              id: In(auditChecklistIds),
              deleted: false,
              companyId: user.companyId,
              status: AuditChkStatus.APPROVED,
            },
            select: ['id'],
          });
        }

        // Handle SA checklists only if saChecklistIds are provided
        let smChecklists = [];
        if (smChecklistIds?.length > 0 && entityParams?.isSA == true) {
          smChecklists = await manager.find(StandardMaster, {
            where: {
              id: In(smChecklistIds),
              deleted: false,
              companyId: user.companyId,
            },
            select: ['id'],
          });
          if (smChecklists?.length !== smChecklistIds?.length) {
            throw new BaseError({ message: 'saChecklist.INVALID' });
          }
        }

        const authorityLists = await manager.find(AuthorityMaster, {
          where: { id: In(authorityIds), deleted: false, companyId: user.companyId },
          select: ['id'],
        });

        const auditType = await manager.findOne(AuditType, {
          where: { id: auditTypeId, deleted: false, companyId: user.companyId },
        });

        if (authorityLists.length !== authorityIds.length) {
          throw new BaseError({ message: 'authority.INVALID' });
        }

        if (!auditType) {
          throw new BaseError({ message: 'auditType.INVALID' });
        }

        // Get current audit checklist of inspection mapping for SYNCHRONIZE with NEW Audit workspaces
        const currentAuditChecklistIds: string[] = (
          await manager.query(
            `
          SELECT imac."auditChecklistId"
          FROM inspection_mapping_audit_checklist imac
          WHERE imac."inspectionMappingId" = $1
        `,
            [inspectionMappingId],
          )
        ).map((item) => item.auditChecklistId);

        // Update inspection mapping
        await manager.save(InspectionMapping, {
          ...inspectionMappingObj,
          id: inspectionMappingId,
          companyId: user.companyId,
          updatedUserId: user.id,
          auditChecklists: auditChecklists as AuditChecklist[],
          standardMasters: smChecklists as StandardMaster[],
          authorities: authorityLists as AuthorityMaster[],
          auditType,
          isSA,
        });

        await manager.delete(InspMapNatFinding, { inspectionMappingId });

        await manager.save(InspMapNatFinding, primaryFindingList);

        await manager.insert(InspectionMappingHistory, {
          inspectionMappingId,
          status: ActionEnum.UPDATE,
          createdUser,
        });

        /**
         * SYNCHRONIZE with NEW Audit workspaces:
         * Update (add new + delete) audit checklist for audit inspection workspace which status is NEW
         */
        // 1. Delete audit checklist
        // const deletedAuditChecklistIds = Array.from(
        //   MySet.difference(new Set(currentAuditChecklistIds), new Set(auditChecklistIds)),
        // );

        // if (deletedAuditChecklistIds.length > 0) {
        //   const qb = manager.createQueryBuilder();

        //   const subQuery = qb
        //     .subQuery()
        //     .select('fillChecklist.id')
        //     .from(FillAuditChecklist, 'fillChecklist')
        //     .innerJoin('fillChecklist.auditWorkspace', 'auditWorkspace')
        //     .where(
        //       `fillChecklist.inspectionMappingId = :inspectionMappingId
        //     AND auditWorkspace.status = :status
        //     AND fillChecklist.auditChecklistId IN (:...deletedAuditChecklistIds)`,
        //       {
        //         inspectionMappingId,
        //         status: AuditWorkspaceStatus.NEW,
        //         deletedAuditChecklistIds,
        //       },
        //     );

        //   const deletedQB = qb
        //     .delete()
        //     .from(FillAuditChecklist)
        //     .where('id IN ' + subQuery.getQuery())
        //     .setParameters(subQuery.getParameters());

        //   await deletedQB.execute();
        // }

        // // 2. Insert fillChecklist for New Audit workspace with new valid audit checklists
        // const newAuditChecklistIds = Array.from(
        //   MySet.difference(new Set(auditChecklistIds), new Set(currentAuditChecklistIds)),
        // );

        // if (newAuditChecklistIds.length > 0) {
        //   // List all new audit workspaces depend on this inspection mapping
        //   const auditWorkspacesNeedUpdate = await manager
        //     .createQueryBuilder(AuditWorkspace, 'auditWorkspace')
        //     .where(
        //       `auditWorkspace.status = :status
        //       AND auditWorkspace.companyId = :companyId
        //       AND :inspectionMappingId = ANY(auditWorkspace.inspectionMappingIds)`,
        //       {
        //         status: AuditWorkspaceStatus.NEW,
        //         companyId: user.companyId,
        //         inspectionMappingId,
        //       },
        //     )
        //     .select(['auditWorkspace.id', 'auditWorkspace.planningRequestId'])
        //     .getMany();

        //   if (auditWorkspacesNeedUpdate.length > 0) {
        //     // Loop all needed update audit workspaces
        //     const newInsAuditChecklistRepo = manager.getCustomRepository(AuditChecklistRepository);
        //     const newInsAuditWorkspaceRepo = manager.getCustomRepository(AuditWorkspaceRepository);
        //     for (let t = 0; t < auditWorkspacesNeedUpdate.length; t++) {
        //       const validAuditChecklists = await newInsAuditChecklistRepo._listValidAuditCheckListsByPR(
        //         auditWorkspacesNeedUpdate[t].planningRequestId,
        //         inspectionMappingId,
        //         newAuditChecklistIds,
        //         [
        //           'id',
        //           'question.id',
        //           'question.locationId',
        //           'inspectionMapping.id',
        //           'auditType.id',
        //         ],
        //       );

        //       if (validAuditChecklists.length > 0) {
        //         await newInsAuditWorkspaceRepo._initFillAuditChecklistAndQuestions(
        //           manager,
        //           auditWorkspacesNeedUpdate[t].id,
        //           validAuditChecklists,
        //         );
        //       }
        //     }
        //   }
        // }
      });
      return rs;
    } catch (ex) {
      LoggerCommon.error(
        '[InspectionMappingRepository] updatedInspectionMapping error ',
        ex.message || ex,
      );
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw new BaseMultiErrors({
          status: 400,
          errors: [{ fieldName: 'auditTypeId', message: 'inspectionMapping.AUDIT_TYPE_EXISTED' }],
        });
      }
      throw ex;
    }
  }

  async deleteInspectionMapping(id: string, user: TokenPayloadModel) {
    await this.connection.transaction(async (manager) => {
      //   const auditWorkspacesNeedUpdate = await manager
      //     .createQueryBuilder(AuditWorkspace, 'auditWorkspace')
      //     .where(
      //       `auditWorkspace.status = :status
      //   AND auditWorkspace.companyId = :companyId
      //   AND :inspectionMappingId = ANY(auditWorkspace.inspectionMappingIds)
      //  `,
      //       {
      //         status: AuditWorkspaceStatus.NEW,
      //         companyId: user.companyId,
      //         inspectionMappingId: id,
      //       },
      //     )
      //     .select([
      //       'auditWorkspace.id',
      //       'auditWorkspace.planningRequestId',
      //       'auditWorkspace.inspectionMappingIds',
      //     ])
      //     .getMany();
      // const deletedAuditChecklistIds: string[] = (
      //   await manager.query(
      //     `
      //     SELECT imac."auditChecklistId"
      //     FROM inspection_mapping_audit_checklist imac
      //     WHERE imac."inspectionMappingId" = $1
      //   `,
      //     [id],
      //   )
      // ).map((item) => item.auditChecklistId);
      const isMappedInspection = await manager
        .createQueryBuilder(PlanningRequest, 'planningRequest')
        .leftJoinAndSelect('planningRequest.auditTypes', 'auditTypes')
        .leftJoin('auditTypes.inspectionMapping', 'inspectionMapping')
        .select([
          'planningRequest.id',
          'auditTypes.id',
          'inspectionMapping.id',
          'planningRequest.status',
        ])
        .where(
          'inspectionMapping.id = :inspectionMappingId AND planningRequest.status != :status',
          {
            inspectionMappingId: id,
            status: PlanningRequestStatus.CANCELLED,
          },
        )
        .getOne();

      if (isMappedInspection?.auditTypes?.length > 0) {
        throw new BaseError({ message: 'common.CANNOT_DELETE_DUE_TO_REF' });
      }

      const deletedResult = await this.delete({
        id,
        companyId: user.companyId,
        deleted: false,
      });

      if (deletedResult?.affected === 0) {
        throw new BaseError({ status: 404, message: 'common.NOT_FOUND' });
      }

      const auditWorkspacesNeedUpdate = await manager
        .createQueryBuilder(AuditWorkspace, 'auditWorkspace')
        .where(
          `auditWorkspace.status = :status 
      AND auditWorkspace.companyId = :companyId
      AND :inspectionMappingId = ANY(auditWorkspace.inspectionMappingIds)
     `,
          {
            status: AuditWorkspaceStatus.NEW,
            companyId: user.companyId,
            inspectionMappingId: id,
          },
        )
        .select([
          'auditWorkspace.id',
          'auditWorkspace.planningRequestId',
          'auditWorkspace.inspectionMappingIds',
        ])
        .getMany();

      if (auditWorkspacesNeedUpdate?.length > 0) {
        const deletedAuditChecklistIds: string[] = (
          await manager.query(
            `
            SELECT imac."auditChecklistId" as id
            FROM inspection_mapping_audit_checklist imac
            WHERE imac."inspectionMappingId" = $1
        
            UNION ALL
        
            SELECT imsa."selfAssessmentId" as id
            FROM inspection_mapping_standard_master imsa
            WHERE imsa."inspectionMappingId" = $1
            `,
            [id],
          )
        )?.map((item) => item?.id);
        for (const auditWorkspaceNeedUpdate of auditWorkspacesNeedUpdate) {
          await manager.save(AuditWorkspace, {
            ...auditWorkspaceNeedUpdate,
            inspectionMappingIds: auditWorkspaceNeedUpdate.inspectionMappingIds.filter(
              (inspectionMapping) => inspectionMapping !== id,
            ),
          });
        }

        const qb = manager.createQueryBuilder();

        const subQuery = qb
          .subQuery()
          .select('fillChecklist.id')
          .from(FillAuditChecklist, 'fillChecklist')
          .innerJoin('fillChecklist.auditWorkspace', 'auditWorkspace')
          .where(
            `fillChecklist.inspectionMappingId = :inspectionMappingId 
        AND auditWorkspace.status = :status
        AND fillChecklist.auditChecklistId IN (:...deletedAuditChecklistIds) or fillChecklist.selfAssesmentId IN (:...deletedAuditChecklistIds)`,
            {
              inspectionMappingId: id,
              status: AuditWorkspaceStatus.NEW,
              deletedAuditChecklistIds,
            },
          );

        const deletedQB = qb
          .delete()
          .from(FillAuditChecklist)
          .where('id IN ' + subQuery.getQuery())
          .setParameters(subQuery.getParameters());

        await deletedQB.execute();
      }
    });
    return 1;
  }
  async _listValidInspectionMappingByAT(auditTypeIds: string[], selectFields: string[]) {
    const qb = this.createQueryBuilder(
      'inspectionMapping',
    ).where(`inspectionMapping.auditTypeId IN (:...auditTypeIds)`, { auditTypeIds });

    if (selectFields) {
      qb.select(
        selectFields.map((field) => {
          if (field.includes('.')) {
            return field;
          } else {
            return 'inspectionMapping.' + field;
          }
        }),
      );
    } else {
      qb.select();
    }

    return this.getManyQB(qb);
  }

  async _checkActiveInspectionMapping(auditTypeIds: string[], companyId: string) {
    const queryBuilder = this.createQueryBuilder('inspectionMapping')
      .leftJoin('inspectionMapping.auditType', 'auditType')
      .leftJoin('inspectionMapping.company', 'company')
      .where(' auditType.id IN (:...auditTypeIds) AND inspectionMapping.status = :status ', {
        auditTypeIds,
        status: CommonStatus.IN_ACTIVE,
      })
      .andWhere('( inspectionMapping.companyId = :companyId OR company.parentId = :companyId)', {
        companyId,
      });
    const inspectionMapping = await queryBuilder.getOne();
    if (inspectionMapping) {
      throw new BaseError({ status: 400, message: 'inspectionMapping.INACTIVE' });
    }
  }
}
