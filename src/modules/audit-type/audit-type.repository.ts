import { EntityRepository } from 'typeorm';
import { BaseError, TokenPayloadModel, TypeORMRepository } from 'svm-nest-lib-v3';
import { AuditType } from './audit-type.entity';
import { InspectionMapping } from '../inspection-mapping/entities/inspection-mapping.entity';
import { ListAuditTypeQueryDto } from './dto';
import { DBErrorCode } from '../../commons/consts/db.const';
import { generateMultipleUniqueFieldsError } from '../../commons/businesses';
import { SelfAssessmentStatusEnum, SelfAssessmentTypeEnum } from 'src/commons/enums';

@EntityRepository(AuditType)
export class AuditTypeRepository extends TypeORMRepository<AuditType> {
  async createAuditType(entityParam: AuditType) {
    try {
      const rs = await this.insert(entityParam);
      return rs.identifiers[0];
    } catch (ex) {
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw await generateMultipleUniqueFieldsError(
          this,
          [
            {
              key: 'code',
              value: entityParam.code,
              errorMessage: 'auditType.AUDIT_CODE_EXISTED',
            },
            {
              key: 'name',
              value: entityParam.name,
              errorMessage: 'auditType.AUDIT_NAME_EXISTED',
            },
          ],
          { key: 'companyId', value: entityParam.companyId },
        );
      }
      throw ex;
    }
  }

  async listAuditTypesByPR(query: ListAuditTypeQueryDto, user: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('auditType')
      .leftJoin('auditType.planningRequests', 'planningRequests')
      .leftJoin('auditType.inspectionMapping', 'inspectionMapping')
      .where('auditType.companyId = :companyId AND planningRequests.id = :planningRequestId', {
        companyId: user.companyId,
        planningRequestId: query.planningRequestId,
      });

    if (query.content) {
      queryBuilder.andWhere('(auditType.code LIKE :content OR auditType.name ILIKE :content)', {
        content: `%${query.content}%`,
      });
    }
    if (query.scope) {
      queryBuilder.andWhere('auditType.scope = :scope', {
        scope: query.scope,
      });
    }

    if (query.isMapping) {
      queryBuilder.andWhere('inspectionMapping.id IS NULL');
    }

    if (query.createdAtFrom) {
      queryBuilder.andWhere('auditType.createdAt >= :createdAtFrom', {
        createdAtFrom: query.createdAtFrom,
      });
    }

    if (query.createdAtTo) {
      queryBuilder.andWhere('auditType.createdAt <= :createdAtTo', {
        createdAtTo: query.createdAtTo,
      });
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'auditType.createdAt:-1',
      },
    );
  }

  async listAuditType(companyId: string, query: ListAuditTypeQueryDto) {
    const queryBuilder = this.createQueryBuilder('auditType')
      .leftJoin('auditType.inspectionMapping', 'inspectionMapping')
      .leftJoin('auditType.company', 'company')
      .addSelect(['company.id', 'company.name'])
      .addSelect(['inspectionMapping.status', 'inspectionMapping.isSA'])
      .where('(auditType.companyId = :companyId OR company.parentId = :companyId)', { companyId });

    if (query.checkExpire) {
      if (query?.isSA == 'true') {
        queryBuilder.leftJoinAndSelect('inspectionMapping.standardMasters', 'standardMasters')
          .leftJoinAndSelect('standardMasters.selfAssessments', 'selfAssessments')
          .leftJoin('standardMasters.createdUser', 'createdUser')
          .leftJoin('standardMasters.updatedUser', 'updatedUser')
          .addSelect([
            'updatedUser.username',
            'createdUser.username',
          ])
          .andWhere('selfAssessments.type = :type and selfAssessments.status IN (:...status) and selfAssessments.deleted = false', { type: SelfAssessmentTypeEnum.OFFICIAL, status: [SelfAssessmentStatusEnum.SUBMITTED, SelfAssessmentStatusEnum.REVIEWED] });
        if (query?.auditCompanyId) {
          queryBuilder.andWhere('selfAssessments.auditCompanyId = :auditCompanyId', {
            auditCompanyId: query?.auditCompanyId,
          });
        }
      } else {
        queryBuilder
          .leftJoin(
            `inspectionMapping.auditChecklists`,
            `auditChecklists`,
            // `auditChecklists.status = 'Approved'`,
          )
          .addSelect([
            'auditChecklists.id',
            'auditChecklists.name',
            'auditChecklists.status',
            'auditChecklists.validityFrom',
            'auditChecklists.validityTo',
          ]);
      }
    }

    if (query.companyId) {
      queryBuilder.andWhere('auditType.companyId = :companyId', {
        companyId: query.companyId,
      });
    }

    if (query.content) {
      queryBuilder.andWhere('(auditType.code LIKE :content OR auditType.name ILIKE :content)', {
        content: `%${query.content}%`,
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('auditType.entityType = :entityType', {
        entityType: query.entityType,
      });
    }

    if (query.scope) {
      queryBuilder.andWhere('auditType.scope = :scope', {
        scope: query.scope,
      });
    }

    if (query.isMapping) {
      queryBuilder.andWhere('inspectionMapping.id IS NULL');
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'auditType.createdAt:-1',
      },
    );
  }

  async getDetailAuditType(companyId: string, auditTypeId: string) {
    const auditType = await this.getOneQB(
      this.createQueryBuilder('auditType')
        .leftJoin('auditType.company', 'company')
        .select()
        .addSelect(['company.name'])
        .where(
          '(auditType.companyId = :companyId OR company.parentId = :companyId) AND auditType.id = :auditTypeId',
          {
            companyId,
            auditTypeId,
          },
        ),
    );

    if (auditType) {
      return auditType;
    } else {
      throw new BaseError({ status: 404, message: 'auditType.NOT_FOUND' });
    }
  }

  async updateAuditType(companyId: string, auditTypeId: string, entityParam: AuditType) {
    try {
      const updateResult = await this.update(
        {
          id: auditTypeId,
          companyId,
          deleted: false,
        },
        entityParam,
      );
      if (updateResult.affected === 0) {
        throw new BaseError({ status: 404, message: 'auditType.NOT_FOUND' });
      }
      return 1;
    } catch (ex) {
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw await generateMultipleUniqueFieldsError(
          this,
          [
            {
              key: 'code',
              value: entityParam.code,
              errorMessage: 'auditType.AUDIT_CODE_EXISTED',
            },
            {
              key: 'name',
              value: entityParam.name,
              errorMessage: 'auditType.AUDIT_NAME_EXISTED',
            },
          ],
          { key: 'companyId', value: companyId },
          auditTypeId,
        );
      }
      throw ex;
    }
  }

  async deleteAuditType(companyId: string, auditTypeId: string) {
    const hasRef = await this.hasRefInGivenTables(
      auditTypeId,
      'auditTypeId',
      [{ entity: InspectionMapping }],
      [
        { tableName: 'fill_audit_checklist', field: 'auditTypeId', noDelCol: false },
        { tableName: 'report_finding_item', field: 'auditTypeId', noDelCol: false },
        { tableName: 'planning_request_audit_type', field: 'auditTypeId', noDelCol: true },
      ],
    );
    if (hasRef) {
      throw new BaseError({ message: 'common.CANNOT_DELETE_DUE_TO_REF' });
    }
    // soft delete
    const updateResult = await this.softDelete({
      id: auditTypeId,
      companyId,
    });
    if (updateResult.affected === 0) {
      throw new BaseError({ status: 404, message: 'auditType.NOT_FOUND' });
    } else {
      return 1;
    }
  }
}
