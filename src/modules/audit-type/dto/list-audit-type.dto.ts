import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos';
import { AuditEntity, AuditTypeScope } from '../../../commons/enums';
export class ListAuditTypeQueryDto extends ListQueryDto {
  @ApiProperty({
    type: 'string',
    required: false,
    enum: AuditEntity,
    description: 'Search for audit entity type',
  })
  @IsOptional()
  entityType?: string;

  @ApiProperty({
    type: 'string',
    required: false,
    enum: AuditTypeScope,
    description: 'Search for audit type scope',
  })
  @IsOptional()
  scope?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  planningRequestId?: string;

  @ApiProperty({
    type: 'boolean',
    description:
      'pass true boolean value to get list audit-type that has not been mapping by inspection-mapping-table ',
  })
  @IsOptional()
  isMapping?: string;

  @ApiProperty({
    type: 'boolean',
    required: false,
  })
  @IsOptional()
  checkExpire?: boolean;

  @ApiProperty({
    type: 'string',
    required: false,
  })
  @IsOptional()
  isSA?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  auditCompanyId?: string;
}
