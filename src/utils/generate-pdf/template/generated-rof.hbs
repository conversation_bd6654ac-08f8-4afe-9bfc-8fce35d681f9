<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
        rel="stylesheet">
    <title>Report of findings template (PDF)</title>
    <style type="text/css">
        html {
            zoom: 0.75;
        }
        .page {
            margin: 0 auto;
            padding: 0;
            width: 90%;
        }

        body {
            font-family: 'Roboto', sans-serif;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            color: #34303e;
        }




        .header {
            height: '0mm' !important;
            border: 1px solid #191919;
            position: relative;
            padding: 10px;
            text-align: center;
            margin-bottom: 10px;
        }

        .header .logo {
            position: absolute;
            left: 3px;
            top: 3px;
            width: 10%;
            height: 93%;
            text-align: center;
        }

        .header .title {
            font-weight: 500;
            font-size: 22px;
            margin-bottom: 10px;
        }

        .header .sub-title {
            font-weight: 500;
            font-size: 18px;
        }

        .table-header {
            margin-bottom: 20px;
        }

        .table-header tr td {
            font-weight: 400;
        }


        table {
            width: 100%;
            /*border-collapse:collapse;*/
            border-top: 1px solid #191919;
            border-left: 1px solid #191919;
            margin-bottom: 10px;
            font-size: 14px;
        }

        table .label {
            text-transform: uppercase;
            font-weight: 500;
        }

        .wrap-content {
            padding-left: 0px;
            padding-right: 0px;
        }

        .wrap-content .label {
            padding-left: 15px;
        }

        .wrap-content .content {

            font-weight: 400;
            border-bottom: solid 1px #b0b0b0;
            padding-left: 15px;
        }

        .wrap-content .sub-content {
            padding-left: 15px;
            font-weight: 500;
        }



        table thead th,
        table tbody td {
            padding: 4px 0 4px 8px;
            border-right: 1px solid #191919;
            border-bottom: 1px solid #191919;
            font-weight: 500;

        }

        .header-sub-content {
            font-weight: 500 !important;
            width: 20%;
        }

        .header-sub-content-data {
            font-weight: 500 !important;
            width: 30%;
        }
        table thead th {
            text-align: center;
            font-weight: 500;

            text-align: left;
            padding: 2px 10px;
        }

        table tbody td img {
            width: 52px;
            height: 31px;
        }

        .table-updated-rof thead th {
            text-decoration: underline;
            text-align: center;
        }

        h1,
        h2,
        h3,
        h4 {
            margin: 0;
            padding: 0;
        }

        .list-images {
            width: 100%;
            display: block;
            page-break-before: auto;
        }

        @media print {
            .list-images {
                page-break-before: auto;
            }
        }

        .wrap-img {
            width: calc(100% / 3);
            float: left;
            box-sizing: border-box;
            border: solid 1px;
            overflow: hidden;
            margin-top: 10px;
        }

        .wrap-img img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .wrap-img .job-title {
            margin-top: 40px;
            margin-left: 3px;
        }
    </style>
</head>

<body>
    <div class="page">
        <div class="header">
            <img src={{this.logoLink}} class="logo" alt="logo" />
            {{#if this.generalInfor.isVessel}}
            <div class="title">VESSEL INSPECTION</div>
            {{else}}
            <div class="title">OFFICE INSPECTION</div>
            {{/if}}
            <div class="sub-title">Report of Findings</div>
        </div>

        <table cellpadding="0" cellspacing="0" class="table-header">
            {{#if this.generalInfor.isVessel}}
            <tbody class="header-content">
                <tr>
                    <td class="header-sub-content">Vessel Name</td>
                    <td class="header-sub-content-data">{{this.generalInfor.vesselName}}</td>
                    <td class="header-sub-content">Vessel Type</td>
                    <td class="header-sub-content-data">{{this.generalInfor.vesselType}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Vessel IMO</td>
                    <td class="header-sub-content-data">{{this.generalInfor.vesselIMO}}</td>
                    <td class="header-sub-content">Ship Management Company</td>
                    <td class="header-sub-content-data">{{this.generalInfor.shipManagementCompany}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Flag</td>
                    <td class="header-sub-content-data">{{this.generalInfor.flag}}</td>
                    <td class="header-sub-content">Date Of Inspection</td>
                    <td class="header-sub-content-data">{{formatDate this.generalInfor.dateOfInspection}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Port Of Inspection</td>
                    <td class="header-sub-content-data">{{this.generalInfor.portOfInspection}}</td>
                    <td class="header-sub-content">Inspector</td>
                    <td class="header-sub-content-data">{{this.generalInfor.inspectors}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Lead Inspector</td>
                    <td class="header-sub-content-data">{{this.generalInfor.leadInspector}}</td>
                    <td class="header-sub-content">Report Date</td>
                    <td class="header-sub-content-data">{{formatDate this.generalInfor.reportDate}}</td>
                </tr>
                 <tr>
                    <td class="header-sub-content">Inspection Type</td>
                    <td class="header-sub-content-data">{{this.generalInfor.inspectionTypes}}</td>
                    <td class="header-sub-content">Inspection Company</td>
                    <td class="header-sub-content-data">{{this.generalInfor.InspectionCompanyName}}</td>
                </tr>
            </tbody>
            {{else}}
            <tbody>
                <tr>
                    <td class="header-sub-content">Company Name</td>
                    <td class="header-sub-content-data">{{this.generalInfor.companyName}}</td>
                    <td class="header-sub-content">Company Type</td>
                    <td class="header-sub-content-data">{{this.generalInfor.companyTypes}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Company IMO</td>
                    <td class="header-sub-content-data">{{this.generalInfor.companyIMO}}</td>
                    <td class="header-sub-content">Date Of Inspection</td>
                    <td class="header-sub-content-data">{{formatDate this.generalInfor.dateOfInspection}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Location</td>
                    <td class="header-sub-content-data">{{this.generalInfor.country}}</td>
                    <td class="header-sub-content">Inspector</td>
                    <td class="header-sub-content-data">{{this.generalInfor.inspectors}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Lead Inspector</td>
                    <td class="header-sub-content-data">{{this.generalInfor.leadInspector}}</td>
                    <td class="header-sub-content">Report Date</td>
                    <td class="header-sub-content-data">{{formatDate this.generalInfor.reportDate}}</td>
                </tr>
                <tr>
                    <td class="header-sub-content">Inspection Company</td>
                    <td class="header-sub-content-data">{{this.generalInfor.InspectionCompanyName}}</td>
                </tr>
            </tbody>
            {{/if}}
        </table>
        {{#each this.data}}
        <p style="font-weight: bold;">{{@index @key}}.{{this.auditType}}</p>
        {{#each this.findingItems}}
        <p>{{@index @key}}.{{this.natureFindingName}}</p>
        <table cellpadding="0" cellspacing="0" class="table-updated-rof">
            <thead>
                <tr>
                    <th style="width: 70px; text-align: center;">
                        S.No
                    </th>
                    <th style="width: 70px; text-align: center;">
                        {{#if ../../this.generalInfor.isSA}}
                            ElementStageQ
                        {{else}}
                            Code
                        {{/if}}
                    </th>
                    <th style="width: 450px;">
                        Item
                    </th>
                    <th style="width: 350px;">
                        Rectification
                    </th>
                </tr>

            </thead>
            <tbody>
                {{#each this.items}}
                <tr>
                    <td style="width: 70px; text-align: center;">{{@index @key}}</td>
                    <td style="width: 70px; text-align: center;">{{this.questionCode}}</td>
                    <td class="wrap-content" style="width: 450px;">
                        <div class="label">
                            {{this.mainCategory}}
                        </div>
                        <div class="content">
                            {{this.question}}
                        </div>

                        <b class="sub-content">
                            Findings: {{this.findingRemark}}
                        </b>
                    </td>
                    <td style="width: 350px;">{{this.recitification}}</td>

                </tr>
                {{/each}}
            </tbody>
        </table>
        {{/each}}
        {{/each}}
        <div class="list-images">
            {{#each this.jobTittles}}
            <div class="wrap-img">
                <img src={{this.link}} class="logo" alt="" />
                <div class="job-title"> {{this}} </div>
            </div>
            {{/each}}
        </div>


    </div>
</body>

</html>
