import { IdentifyEntity } from 'svm-nest-lib-v3';
import {
  AfterLoad,
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import {
  IncidentInvestigationReviewStatus,
  IncidentOriginEnum,
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
} from '../../../commons/enums';
import { hashAttachmentValues } from '../../../commons/functions';
import { CreatedUserHistoryModel } from '../../../commons/models';
import { IncidentMaster } from '../../../modules-qa/incident-master/incident-master.entity';
import { Company } from '../../../modules/company/company.entity';
import { PortMaster } from '../../../modules/port-master/port-master.entity';
import { UserAssignment } from '../../../modules/user-assignment/user-assignment.entity';
import { Vessel } from '../../../modules/vessel/entity/vessel.entity';
import { IncidentInvestigationComment } from './incident-investigation-comment.entity';
import { IncidentInvestigationHistory } from './incident-investigation-history.entity';
import { IncidentInvestigationRemark } from './incident-investigation-remark.entity';
import { IncidentInvestigationReview } from './incident-investigation-review.entity';
import { SubIncidentType } from '../../../modules-qa/sub-incident-type/sub-incident-type.entity';
import { Injury } from '../../injury/entity/injury.entity';
import { VoyageType } from '../../../modules/voyage-type/entities/voyage-type.entity';

export enum IncidentInvestigationStatus {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
  REVIEWED = 'Reviewed',
  REASSIGN = 'Reassign',
  CLOSEOUT = 'Close out',
}

@Entity()
export class IncidentInvestigation extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column()
  public description: string;

  @Column()
  public title: string;

  @Column({ nullable: true })
  public sNo: string;

  @Column({ nullable: true })
  public refId: string;

  @Column({ nullable: true })
  public voyageNo: string;

  @Column({ type: 'smallint', nullable: true })
  public totalNumberOfCrew: number;

  @Column({ type: 'timestamp' })
  public dateTimeOfIncident: Date;

  @Column({ type: 'integer', nullable: true })
  public dateTimeOfIncident_Year: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  public dateTimeOfIncident_Month: string;

  @Column({ nullable: true })
  public otherType: string;

  @Column({ nullable: true, default: false })
  public atPort: boolean;

  @Column({ nullable: true })
  public latitude: string;

  @Column({ nullable: true })
  public longitude: string;

  @Column({ nullable: true })
  public typeOfLoss: string;

  @Column({ nullable: true })
  public immediateDirectCause: string;

  @Column({ nullable: true })
  public basicUnderlyingCauses: string;

  @Column({ nullable: true })
  public rootCause: string;

  @Column({ nullable: true })
  public contributionFactor: string;

  @Column({ nullable: true })
  public nonContributionFactor: string;

  @Column({ nullable: true })
  public immediateAction: string;

  @Column({ nullable: true })
  public preventiveAction: string;

  @Column({ nullable: true })
  public correctionAction: string;

  @Column({ nullable: true })
  public actionControlNeeds: string;

  @Column({
    type: 'smallint',
    enum: VesselScreeningPotentialRiskEnum,
    nullable: true,
  })
  public potentialRisk: VesselScreeningPotentialRiskEnum;

  @Column({
    type: 'smallint',
    nullable: true,
  })
  public potentialScore: number;

  @Column({
    type: 'smallint',
    enum: VesselScreeningObservedRiskEnum,
    nullable: true,
  })
  public observedRisk: VesselScreeningObservedRiskEnum;

  @Column({
    type: 'smallint',
    nullable: true,
  })
  public observedScore: number;

  @Column({
    nullable: true,
  })
  public timeLoss: boolean;

  @Column({
    type: 'enum',
    enum: IncidentInvestigationReviewStatus,
    nullable: true,
  })
  public reviewStatus: string;

  @Column({
    type: 'enum',
    enum: IncidentInvestigationStatus,
    nullable: true,
  })
  public status: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  @Column({
    type: 'uuid',
    array: true,
    default: [],
  })
  public attachments: string[];

  @Column({ nullable: true, type: 'json' })
  createdUser?: CreatedUserHistoryModel;

  @Column({ nullable: true, type: 'json' })
  updatedUser?: CreatedUserHistoryModel;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid' })
  public vesselId: string;

  @Column({ type: 'uuid' })
  public portId: string;

  @Column({ type: 'uuid', nullable: true })
  public portToId: string;

  @Column({ type: 'enum', enum: IncidentOriginEnum, default: IncidentOriginEnum.MANUAL })
  public incidentOrigin: string;

  @Column({ type: 'uuid', nullable: true })
  public voyageTypeId: string;

  @Column({ type: 'integer', nullable: true })
  public fatalities?: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  public pollution?: number;

  // relationship
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => Vessel, { onDelete: 'CASCADE' })
  vessel: Vessel;

  @ManyToOne(() => PortMaster, { onDelete: 'CASCADE' })
  port: PortMaster;

  @ManyToOne(() => PortMaster, { onDelete: 'CASCADE' })
  portTo: PortMaster;

  @ManyToMany(() => IncidentMaster)
  @JoinTable({ name: 'incident_investigation_incident_master' })
  typeIncidents: IncidentMaster[];


  @ManyToMany(() => SubIncidentType)
  @JoinTable({ name: 'incident_investigation_sub_incident_type' })
  subIncidents: SubIncidentType[];

  @ManyToMany(() => IncidentMaster)
  @JoinTable({ name: 'incident_investigation_secondary_incident_master' })
  secondaryIncidents: IncidentMaster[];

  @ManyToMany(() => SubIncidentType)
  @JoinTable({ name: 'incident_investigation_secondary_sub_incident_type' })
  secondarySubIncidents: SubIncidentType[];

  @OneToMany(
    () => IncidentInvestigationRemark,
    (incidentInvestigationRemark) => incidentInvestigationRemark.incidentInvestigation,
  )
  incidentInvestigationRemarks: IncidentInvestigationRemark[];



  @OneToMany(
    () => IncidentInvestigationReview,
    (incidentInvestigationReview) => incidentInvestigationReview.incidentInvestigation,
  )
  incidentInvestigationReviews: IncidentInvestigationReview[];

  @OneToMany(
    () => IncidentInvestigationComment,
    (incidentInvestigationComment) => incidentInvestigationComment.incidentInvestigation,
  )
  incidentInvestigationComments: IncidentInvestigationComment[];

  @OneToMany(
    () => IncidentInvestigationHistory,
    (incidentInvestigationHistory) => incidentInvestigationHistory.incidentInvestigation,
  )
  incidentInvestigationHistories: IncidentInvestigationHistory[];

  @OneToMany(() => UserAssignment, (userAssignment) => userAssignment.incidentInvestigation)
  userAssignments: UserAssignment[];

  @OneToMany(() => Injury, (injury) => injury.incidentInvestigation)
  public injuries: Injury[];

  @ManyToOne(() => VoyageType, { onDelete: 'NO ACTION' })
  voyageType: VoyageType;

  @AfterLoad()
  async transformAttachment() {
    this.attachments = await hashAttachmentValues(this.attachments);
  }
}
