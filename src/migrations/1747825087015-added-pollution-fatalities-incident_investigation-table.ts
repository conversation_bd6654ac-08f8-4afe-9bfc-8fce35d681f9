import {MigrationInterface, QueryRunner} from "typeorm";

export class addedPollutionFatalitiesIncidentInvestigationTable1747825087015 implements MigrationInterface {
    name = 'addedPollutionFatalitiesIncidentInvestigationTable1747825087015'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "public"."incident_investigation" ADD "fatalities" integer`);
        await queryRunner.query(`ALTER TABLE "public"."incident_investigation" ADD "pollution" numeric(10,2)`);
        }

    public async down(queryRunner: QueryRunner): Promise<void> {}

}
